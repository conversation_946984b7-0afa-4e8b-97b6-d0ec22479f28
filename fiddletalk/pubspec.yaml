name: fiddletalk
description: "FiddleTalk - 小提琴乐器交流心得社区应用"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI组件和图标
  cupertino_icons: ^1.0.8

  # 状态管理
  provider: ^6.1.2

  # 路由管理
  go_router: ^14.2.7

  # 网络请求
  dio: ^5.4.3+1

  # 本地存储
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # URL启动器
  url_launcher: ^6.2.6

  # 内购功能
  in_app_purchase: ^3.1.11
  in_app_purchase_storekit: ^0.3.6+10

  # 图片处理
  cached_network_image: ^3.3.1
  image_picker: ^1.0.8
  path_provider: ^2.1.4

  # 权限管理
  permission_handler: ^11.3.1

  # 语音识别
  speech_to_text: ^6.6.2

  # UI增强
  flutter_staggered_grid_view: ^0.7.0
  pull_to_refresh: ^2.0.0
  shimmer: ^3.0.0

  # 工具类
  intl: ^0.19.0
  uuid: ^4.4.0
  path: ^1.9.0

  # 动画
  lottie: ^3.1.2

  # 字体图标
  font_awesome_flutter: ^10.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  # 代码生成
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # 应用资源文件
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - assets/violin/
    - assets/violinist/
    - assets/Violin score/
    - assets/actor/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # 自定义字体（暂时注释掉，使用系统默认字体）
  # fonts:
  #   - family: PingFang
  #     fonts:
  #       - asset: assets/fonts/PingFang-Regular.ttf
  #       - asset: assets/fonts/PingFang-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/PingFang-Bold.ttf
  #         weight: 700
