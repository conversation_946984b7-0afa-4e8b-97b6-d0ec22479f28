import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/providers/user_data_provider.dart';
import '../../shared/widgets/avatar_selector.dart';

/// Edit Profile Screen
class EditProfileScreen extends StatefulWidget {
  final Map<String, dynamic> userData;
  
  const EditProfileScreen({
    super.key,
    required this.userData,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nicknameController;
  late TextEditingController _bioController;
  late TextEditingController _learningYearsController;
  late TextEditingController _specialtyStyleController;
  late TextEditingController _personalGoalController;

  String? _avatarPath;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nicknameController = TextEditingController(text: widget.userData['nickname'] ?? '');
    _bioController = TextEditingController(text: widget.userData['bio'] ?? '');
    _learningYearsController = TextEditingController(text: widget.userData['learningYears'] ?? '');
    _specialtyStyleController = TextEditingController(text: widget.userData['specialtyStyle'] ?? '');
    _personalGoalController = TextEditingController(text: widget.userData['personalGoal'] ?? '');
    _avatarPath = widget.userData['avatar'];
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _bioController.dispose();
    _learningYearsController.dispose();
    _specialtyStyleController.dispose();
    _personalGoalController.dispose();
    super.dispose();
  }



  void _selectAvatar() async {
    final selectedAvatar = await _showAvatarSelector();

    if (selectedAvatar != null) {
      setState(() {
        _avatarPath = selectedAvatar;
      });
    }
  }

  Future<String?> _showAvatarSelector() async {
    String? selectedAvatar;
    
    await showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AvatarSelector(
        currentAvatar: _avatarPath,
        onAvatarSelected: (avatar) {
          selectedAvatar = avatar;
        },
      ),
    );
    
    return selectedAvatar;
  }

  void _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Get the UserDataProvider instance
        final userDataProvider = Provider.of<UserDataProvider>(context, listen: false);

        // Update user profile data
        await userDataProvider.updateProfile(
          avatar: _avatarPath,
          nickname: _nicknameController.text.trim(),
          bio: _bioController.text.trim(),
          learningYears: _learningYearsController.text.trim(),
          specialtyStyle: _specialtyStyleController.text.trim(),
          personalGoal: _personalGoalController.text.trim(),
        );

        setState(() {
          _isLoading = false;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Return updated data
        Navigator.of(context).pop({
          'nickname': _nicknameController.text.trim(),
          'bio': _bioController.text.trim(),
          'learningYears': _learningYearsController.text.trim(),
          'specialtyStyle': _specialtyStyleController.text.trim(),
          'personalGoal': _personalGoalController.text.trim(),
          'avatar': _avatarPath,
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to save profile: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        title: Text(
          'Edit Profile',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          color: AppColors.textPrimary,
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: Text(
              'Save',
              style: AppTextStyles.button.copyWith(
                color: _isLoading ? AppColors.textTertiary : AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        iconTheme: const IconThemeData(color: AppColors.textPrimary),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.surface,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAvatarSection(),
            const SizedBox(height: 24),
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildDetailInfoSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: _selectAvatar,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 3,
                ),
              ),
              child: ClipOval(
                child: _avatarPath != null
                    ? _buildAvatarImage()
                    : Container(
                        color: AppColors.surfaceVariant,
                        child: const Icon(
                          Icons.person,
                          size: 50,
                          color: AppColors.textTertiary,
                        ),
                      ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Tap to change avatar',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarImage() {
    // Show the selected avatar (always an asset)
    if (_avatarPath != null && _avatarPath!.isNotEmpty) {
      return Image.asset(
        _avatarPath!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: AppColors.surfaceVariant,
          child: const Icon(
            Icons.person,
            size: 50,
            color: AppColors.textTertiary,
          ),
        ),
      );
    }

    // Default fallback
    return Container(
      color: AppColors.surfaceVariant,
      child: const Icon(
        Icons.person,
        size: 50,
        color: AppColors.textTertiary,
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _nicknameController,
            label: 'Display Name',
            hint: 'Enter your display name',
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Display name is required';
              }
              if (value.trim().length < 2) {
                return 'Display name must be at least 2 characters';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _bioController,
            label: 'Bio',
            hint: 'Tell us about yourself',
            maxLines: 3,
            maxLength: 150,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Musical Background',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _learningYearsController,
            label: 'Learning Years',
            hint: 'e.g., 3 years',
            maxLength: 20,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _specialtyStyleController,
            label: 'Specialty Style',
            hint: 'e.g., Classical Music',
            maxLength: 50,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _personalGoalController,
            label: 'Personal Goal',
            hint: 'e.g., Master Bach\'s works fluently',
            maxLines: 2,
            maxLength: 100,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    int? maxLength,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.subtitle2.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          maxLength: maxLength,
          validator: validator,
          style: AppTextStyles.body1,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTextStyles.body1.copyWith(
              color: AppColors.textTertiary,
            ),
            filled: true,
            fillColor: AppColors.surfaceVariant,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
