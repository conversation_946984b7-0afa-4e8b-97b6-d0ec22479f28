import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/mock_data_service.dart';
import '../../shared/models/post_model.dart';
import '../../shared/widgets/post_card.dart';
import '../../shared/providers/user_data_provider.dart';
import '../../shared/providers/global_data_provider.dart';
import '../post/post_detail_screen.dart';
import '../post/create_post_screen.dart';

/// My Posts Screen - Display current user's posts
class MyPostsScreen extends StatefulWidget {
  const MyPostsScreen({super.key});

  @override
  State<MyPostsScreen> createState() => _MyPostsScreenState();
}

class _MyPostsScreenState extends State<MyPostsScreen> {
  final MockDataService _dataService = MockDataService();
  final UserDataProvider _userDataProvider = UserDataProvider();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  List<PostModel> _myPosts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMyPosts();
  }

  Future<void> _loadMyPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取所有帖子，然后过滤出当前用户的帖子
      final allPosts = await _dataService.getPosts(page: 1, limit: 100);
      final myPosts = allPosts.where((post) => post.authorId == _userDataProvider.currentUserId).toList();
      
      setState(() {
        _myPosts = myPosts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('Failed to load posts: $e');
    }
  }

  Future<void> _onRefresh() async {
    await _loadMyPosts();
  }

  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  void _toggleLike(PostModel post) {
    setState(() {
      final index = _myPosts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        _myPosts[index] = _myPosts[index].copyWith(
          isLiked: !_myPosts[index].isLiked,
          likesCount: _myPosts[index].isLiked 
              ? _myPosts[index].likesCount - 1 
              : _myPosts[index].likesCount + 1,
        );
      }
    });
  }

  void _toggleCollect(PostModel post) {
    setState(() {
      final index = _myPosts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        _myPosts[index] = _myPosts[index].copyWith(
          isCollected: !_myPosts[index].isCollected,
          collectionsCount: _myPosts[index].isCollected 
              ? _myPosts[index].collectionsCount - 1 
              : _myPosts[index].collectionsCount + 1,
        );
      }
    });
  }

  void _sharePost(PostModel post) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        title: Text(
          'My Posts',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          color: AppColors.textPrimary,
          onPressed: () => Navigator.of(context).pop(),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.surface,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    if (_myPosts.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      color: AppColors.primary,
      onRefresh: _onRefresh,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: _myPosts.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final post = _myPosts[index];
          return PostCard(
            title: post.title,
            content: post.content,
            author: post.author?.nickname ?? 'You',
            authorAvatar: post.author?.avatar,
            authorId: post.authorId,
            category: post.category,
            primaryImage: post.primaryImage,
            images: post.images,
            tags: post.tags,
            likesCount: post.likesCount,
            commentsCount: _globalDataProvider.getPostCommentsCount(post.id),
            collectionsCount: post.collectionsCount,
            isLiked: post.isLiked,
            isCollected: post.isCollected,
            isHighlighted: post.isHighlighted,
            isFollowed: false, // Always false for own posts
            timeAgo: post.formattedCreatedAt,
            onTap: () {
              _navigateToPostDetail(post);
            },
            onLike: () {
              _toggleLike(post);
            },
            onComment: () {
              _navigateToPostDetail(post);
            },
            onCollect: () {
              _toggleCollect(post);
            },
            onShare: () {
              _sharePost(post);
            },
            // No onFollow and onMoreActions for own posts
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.article_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Posts Yet',
            style: AppTextStyles.headline6.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start sharing your violin experiences\nwith the community',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textSecondary,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreatePostScreen(),
                ),
              );
              
              // If a post was created, refresh the posts
              if (result == true) {
                _loadMyPosts();
              }
            },
            icon: const Icon(Icons.add),
            label: const Text('Create Your First Post'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 