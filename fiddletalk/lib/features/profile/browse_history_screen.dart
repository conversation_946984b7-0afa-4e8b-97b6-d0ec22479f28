import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/user_model.dart';
import '../../shared/providers/global_data_provider.dart';
import '../../shared/widgets/post_card.dart';
import '../../shared/widgets/user_action_dialog.dart';
import '../post/post_detail_screen.dart';
import '../author/author_profile_screen.dart';
import 'dart:async';

/// Browse History Screen - Display user's browsing history
class BrowseHistoryScreen extends StatefulWidget {
  const BrowseHistoryScreen({super.key});

  @override
  State<BrowseHistoryScreen> createState() => _BrowseHistoryScreenState();
}

class _BrowseHistoryScreenState extends State<BrowseHistoryScreen> {
  final RefreshController _refreshController = RefreshController();
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();

  List<PostModel> _historyPosts = [];
  bool _isLoading = false;
  StreamSubscription<PostModel>? _postUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _loadBrowseHistory();
    _setupPostStateListener();
    _globalDataProvider.initialize();
    _globalDataProvider.addListener(_onGlobalDataChanged);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _postUpdateSubscription?.cancel();
    _globalDataProvider.removeListener(_onGlobalDataChanged);
    super.dispose();
  }

  void _onGlobalDataChanged() {
    if (mounted) {
      setState(() {
        // Refresh UI when global data changes
      });
    }
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      setState(() {
        final index = _historyPosts.indexWhere((post) => post.id == updatedPost.id);
        if (index != -1) {
          _historyPosts[index] = updatedPost;
        }
      });
    });
  }

  Future<void> _loadBrowseHistory() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _globalDataProvider.initialize();
      final historyPostIds = _globalDataProvider.browseHistoryIds;
      final allPosts = await _dataService.getPosts(page: 1, limit: 100);

      // 初始化PostStateService中的帖子状态
      _postStateService.initializePosts(allPosts);

      // Filter posts based on browse history and remove blocked users' posts
      _historyPosts = allPosts
          .where((post) => historyPostIds.contains(post.id))
          .toList();

      _historyPosts = _globalDataProvider.filterPosts(_historyPosts);

      // Sort by browse time (most recent first)
      _historyPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    } catch (e) {
      print('Error loading browse history: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    await _loadBrowseHistory();
    _refreshController.refreshCompleted();
  }

  void _onLoading() {
    // Simulate loading more data
    Future.delayed(const Duration(seconds: 1), () {
      _refreshController.loadComplete();
    });
  }

  /// 导航到帖子详情页
  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  /// 导航到作者主页
  void _navigateToAuthorProfile(UserModel author) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AuthorProfileScreen(author: author),
      ),
    );
  }

  /// 切换点赞状态
  void _toggleLike(PostModel post) {
    _postStateService.toggleLike(post.id);
  }

  /// 切换收藏状态
  void _toggleCollect(PostModel post) {
    _postStateService.toggleCollect(post.id);
  }

  /// 分享帖子
  void _sharePost(PostModel post) {
    // TODO: Implement share functionality
  }

  /// 切换关注状态
  void _toggleFollow(PostModel post) {
    if (post.author != null) {
      _globalDataProvider.toggleUserFollow(post.author!.id);
      setState(() {
        // Refresh UI to show updated follow status
      });
    }
  }

  /// 显示更多操作菜单
  void _showMoreActions(PostModel post) {
    if (post.author == null) return;

    showDialog(
      context: context,
      builder: (context) => UserActionDialog(
        userName: post.author!.nickname,
        onBlock: () => _blockUser(post.author!),
        onReport: () => _reportUser(post.author!, ['Inappropriate content']),
      ),
    );
  }

  /// 拉黑用户
  Future<void> _blockUser(UserModel user) async {
    try {
      await _globalDataProvider.initialize();
      await _globalDataProvider.blockUser(user.id);
      
      // 刷新帖子列表，过滤被拉黑用户的帖子
      setState(() {
        _historyPosts = _globalDataProvider.filterPosts(_historyPosts);
      });
    } catch (e) {
      print('Error blocking user: $e');
    }
  }

  /// 举报用户
  Future<void> _reportUser(UserModel user, List<String> reasons) async {
    try {
      await _globalDataProvider.initialize();
      await _globalDataProvider.reportUser(user.id, reasons);
    } catch (e) {
      print('Error reporting user: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'Browse History',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textPrimary),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.surface,
          statusBarIconBrightness: Brightness.dark,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearHistory,
            tooltip: 'Clear History',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_historyPosts.isEmpty) {
      return _buildEmptyState();
    }

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: false,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: _historyPosts.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final post = _historyPosts[index];
          // 获取最新的帖子状态
          final currentState = _postStateService.getPostState(post.id) ?? post;
          return PostCard(
            title: currentState.title,
            content: currentState.content,
            author: currentState.author?.nickname ?? 'Unknown User',
            authorAvatar: currentState.author?.avatar,
            authorId: currentState.authorId,
            category: currentState.category,
            primaryImage: currentState.primaryImage,
            images: currentState.images,
            tags: currentState.tags,
            likesCount: currentState.likesCount,
            commentsCount: _globalDataProvider.getPostCommentsCount(currentState.id),
            collectionsCount: currentState.collectionsCount,
            isLiked: currentState.isLiked,
            isCollected: currentState.isCollected,
            isHighlighted: currentState.isHighlighted,
            isFollowed: _globalDataProvider.isUserFollowed(currentState.authorId),
            timeAgo: currentState.formattedCreatedAt,
            onTap: () => _navigateToPostDetail(currentState),
            onLike: () => _toggleLike(currentState),
            onComment: () => _navigateToPostDetail(currentState),
            onCollect: () => _toggleCollect(currentState),
            onShare: () => _sharePost(currentState),
            onAuthorTap: () => _navigateToAuthorProfile(currentState.author!),
            onFollow: () => _toggleFollow(currentState),
            onMoreActions: () => _showMoreActions(currentState),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'No browse history',
            style: AppTextStyles.headline6.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Posts you view will appear here',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  /// 清空历史记录
  Future<void> _clearHistory() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear History'),
        content: const Text('Are you sure you want to clear all browse history?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _globalDataProvider.initialize();
        await _globalDataProvider.clearBrowseHistory();
        setState(() {
          _historyPosts.clear();
        });
      } catch (e) {
        print('Error clearing history: $e');
      }
    }
  }
}
