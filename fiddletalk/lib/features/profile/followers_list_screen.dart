import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/models/user_model.dart';
import '../../shared/providers/global_data_provider.dart';
import '../author/author_profile_screen.dart';

/// Followers List Screen - Display users who follow the current user
class FollowersListScreen extends StatefulWidget {
  const FollowersListScreen({super.key});

  @override
  State<FollowersListScreen> createState() => _FollowersListScreenState();
}

class _FollowersListScreenState extends State<FollowersListScreen> {
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  List<UserModel> _followers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFollowers();
  }

  Future<void> _loadFollowers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate loading followers (in real app, this would be an API call)
      await Future.delayed(const Duration(milliseconds: 800));
      
      // Mock followers data
      final followers = _generateMockFollowers();
      
      setState(() {
        _followers = followers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Generate mock followers data
  List<UserModel> _generateMockFollowers() {
    return [
      UserModel(
        id: 'follower_1',
        username: 'violin_master',
        nickname: 'Violin Master',
        avatar: 'assets/actor/boy.png',
        bio: 'Professional violinist with 15+ years experience',
        email: '<EMAIL>',
        level: 5,
        coins: 2500,
        followersCount: 1200,
        followingCount: 150,
        postsCount: 89,
        badges: ['Professional Player', 'Master'],
        createdAt: DateTime.now().subtract(const Duration(days: 365 * 5)),
        updatedAt: DateTime.now(),
        isVerified: true,
        learningYears: '15+ years',
        specialtyStyle: 'Classical & Jazz',
        personalGoal: 'Teaching the next generation',
        isOnline: true,
        lastActiveAt: DateTime.now(),
      ),
      UserModel(
        id: 'follower_2',
        username: 'music_lover_sarah',
        nickname: 'Sarah Chen',
        avatar: 'assets/actor/girl.png',
        bio: 'Amateur violinist, love sharing musical moments',
        email: '<EMAIL>',
        level: 3,
        coins: 800,
        followersCount: 45,
        followingCount: 120,
        postsCount: 23,
        badges: ['Active User', 'Music Lover'],
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        updatedAt: DateTime.now(),
        isVerified: false,
        learningYears: '2 years',
        specialtyStyle: 'Pop Music',
        personalGoal: 'Play my favorite songs',
        isOnline: false,
        lastActiveAt: DateTime.now().subtract(const Duration(hours: 3)),
      ),
      UserModel(
        id: 'follower_3',
        username: 'classical_enthusiast',
        nickname: 'David Kumar',
        avatar: null,
        bio: 'Classical music enthusiast and student',
        email: '<EMAIL>',
        level: 2,
        coins: 450,
        followersCount: 28,
        followingCount: 89,
        postsCount: 15,
        badges: ['Student', 'Classical Lover'],
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now(),
        isVerified: false,
        learningYears: '1 year',
        specialtyStyle: 'Classical Music',
        personalGoal: 'Master Bach pieces',
        isOnline: true,
        lastActiveAt: DateTime.now(),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        title: Text(
          'Followers',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          color: AppColors.textPrimary,
          onPressed: () => Navigator.of(context).pop(),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.surface,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            )
          : _followers.isEmpty
              ? _buildEmptyState()
              : _buildFollowersList(),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 24),
          Text(
            'No Followers Yet',
            style: AppTextStyles.headline5.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Share interesting content to attract more followers!',
              style: AppTextStyles.body1.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Build followers list
  Widget _buildFollowersList() {
    return Column(
      children: [
        // Header with count
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                offset: const Offset(0, 1),
                blurRadius: 4,
              ),
            ],
          ),
          child: Text(
            '${_followers.length} Followers',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        // Followers list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _followers.length,
            itemBuilder: (context, index) {
              final follower = _followers[index];
              return _buildFollowerCard(follower);
            },
          ),
        ),
      ],
    );
  }

  /// Build follower card
  Widget _buildFollowerCard(UserModel follower) {
    final isFollowing = _globalDataProvider.isUserFollowed(follower.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToUserProfile(follower),
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            // Avatar
            _buildAvatar(follower),
            const SizedBox(width: 12),
            // User info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        follower.nickname,
                        style: AppTextStyles.subtitle1.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      if (follower.isVerified) ...[
                        const SizedBox(width: 4),
                        Icon(
                          Icons.verified,
                          size: 16,
                          color: AppColors.primary,
                        ),
                      ],
                      if (follower.isOnline) ...[
                        const SizedBox(width: 8),
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '@${follower.username}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                  if (follower.bio != null) ...[
                    const SizedBox(height: 6),
                    Text(
                      follower.bio!,
                      style: AppTextStyles.body2.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildStatChip('${follower.postsCount} Posts'),
                      const SizedBox(width: 8),
                      _buildStatChip('${follower.followersCount} Followers'),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            // Follow button
            _buildFollowButton(follower, isFollowing),
          ],
        ),
      ),
    );
  }

  /// Build avatar
  Widget _buildAvatar(UserModel user) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: user.avatar != null
            ? Image.asset(
                user.avatar!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// Build default avatar
  Widget _buildDefaultAvatar() {
    return Container(
      color: AppColors.primary.withOpacity(0.1),
      child: Icon(
        Icons.person,
        size: 28,
        color: AppColors.primary,
      ),
    );
  }

  /// Build stat chip
  Widget _buildStatChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build follow button
  Widget _buildFollowButton(UserModel user, bool isFollowing) {
    return SizedBox(
      width: 80,
      height: 32,
      child: ElevatedButton(
        onPressed: () => _toggleFollow(user),
        style: ElevatedButton.styleFrom(
          backgroundColor: isFollowing ? AppColors.surface : AppColors.primary,
          foregroundColor: isFollowing ? AppColors.textSecondary : AppColors.textOnPrimary,
          elevation: 0,
          side: isFollowing
              ? BorderSide(color: AppColors.divider)
              : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          isFollowing ? 'Following' : 'Follow',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Navigate to user profile
  void _navigateToUserProfile(UserModel user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AuthorProfileScreen(author: user),
      ),
    );
  }

  /// Toggle follow status
  void _toggleFollow(UserModel user) {
    setState(() {
      _globalDataProvider.toggleUserFollow(user.id);
    });
  }
} 