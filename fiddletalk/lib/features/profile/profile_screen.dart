import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/agreement_service.dart';

import '../../shared/widgets/profile_header.dart';
import '../../shared/widgets/profile_stats.dart';
import '../../shared/widgets/profile_menu_item.dart';
import '../../shared/providers/user_data_provider.dart';
import '../liked/liked_posts_screen.dart';
import '../saved/saved_posts_screen.dart';
import 'blocked_users_screen.dart';
import 'following_list_screen.dart';
import 'followers_list_screen.dart';
import 'browse_history_screen.dart';
import 'edit_profile_screen.dart';
import 'feedback_screen.dart';
import 'achievements_screen.dart';
import 'my_home_page_screen.dart';
import 'my_posts_screen.dart';
import '../coin_system/fiddletalk_coin_store_screen.dart';

/// Profile Screen - My Violin Community Hub
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;



  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer<UserDataProvider>(
      builder: (context, userDataProvider, child) {
        final userData = userDataProvider.userData;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildAppBar(),
              _buildProfileHeader(userData),
              _buildProfileStats(userData),
              const SizedBox(height: 16),
              _buildMenuSection(),
            ],
          ),
        ),
      ),
    );
      },
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          // Title
          Expanded(
            child: Text(
              'Profile',
              style: AppTextStyles.headline6.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(Map<String, dynamic> userData) {
    return ProfileHeader(
      avatar: userData['avatar'],
      nickname: userData['nickname'],
      bio: userData['bio'],
      level: userData['level'],
      coins: userData['coins'],
      badges: List<String>.from(userData['badges']),
      learningYears: userData['learningYears'],
      specialtyStyle: userData['specialtyStyle'],
      personalGoal: userData['personalGoal'],
      onEditProfile: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EditProfileScreen(userData: userData),
          ),
        );

        if (result != null && result is Map<String, dynamic>) {
          // Update the global user data provider
          await Provider.of<UserDataProvider>(context, listen: false).updateProfile(
            avatar: result['avatar'],
            nickname: result['nickname'],
            bio: result['bio'],
            learningYears: result['learningYears'],
            specialtyStyle: result['specialtyStyle'],
            personalGoal: result['personalGoal'],
          );
        }
      },
    );
  }

  Widget _buildProfileStats(Map<String, dynamic> userData) {
    return ProfileStats(
      followingCount: userData['followingCount'],
      postsCount: userData['postsCount'],
      onFollowingPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FollowingListScreen(),
          ),
        );
      },
      onPostsPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MyHomePageScreen(),
          ),
        );
      },
    );
  }

  Widget _buildMenuSection() {
    return Consumer<UserDataProvider>(
      builder: (context, userDataProvider, child) {
        final userData = userDataProvider.userData;
        return Container(
          color: AppColors.surface,
          child: Column(
            children: [
              // Content management
              _buildMenuGroup(
            title: 'Content Management',
            items: [
              ProfileMenuItem(
                icon: Icons.article_outlined,
                title: 'My Posts',
                subtitle: 'View all my published content',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyPostsScreen(),
                    ),
                  );
                },
              ),
              ProfileMenuItem(
                icon: Icons.favorite_outline,
                title: 'Liked Posts',
                subtitle: 'Posts you liked',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LikedPostsScreen(),
                    ),
                  );
                },
              ),
              ProfileMenuItem(
                icon: Icons.bookmark_outline,
                title: 'Saved Posts',
                subtitle: 'Posts you saved',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SavedPostsScreen(),
                    ),
                  );
                },
              ),
              ProfileMenuItem(
                icon: Icons.history,
                title: 'Browse History',
                subtitle: 'Recently viewed content',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BrowseHistoryScreen(),
                    ),
                  );
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Social management
          _buildMenuGroup(
            title: 'Social Management',
            items: [
              ProfileMenuItem(
                icon: Icons.people_outline,
                title: 'Following',
                subtitle: 'Players I follow',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FollowingListScreen(),
                    ),
                  );
                },
              ),
              ProfileMenuItem(
                icon: Icons.block_outlined,
                title: 'Blocked Users',
                subtitle: 'Manage blocked users',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BlockedUsersScreen(),
                    ),
                  );
                },
              ),

            ],
          ),

          const SizedBox(height: 16),

          // Coin system
          _buildMenuGroup(
            title: 'Coin System',
            items: [
              ProfileMenuItem(
                icon: Icons.monetization_on_outlined,
                title: 'Coin Store',
                subtitle: 'Buy coins, support creators',
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${userData['coins']}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.accent,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FiddleTalkCoinStoreScreen(),
                    ),
                  );
                },
              ),

            ],
          ),

          const SizedBox(height: 16),

          // Growth & Settings
          _buildMenuGroup(
            title: 'Growth & Settings',
            items: [
              ProfileMenuItem(
                icon: Icons.emoji_events_outlined,
                title: 'Achievements',
                subtitle: 'View earned badges',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AchievementsScreen(),
                    ),
                  );
                },
              ),
              ProfileMenuItem(
                icon: Icons.privacy_tip_outlined,
                title: 'Privacy Policy',
                subtitle: 'Read our privacy policy',
                onTap: _openPrivacyPolicy,
              ),
              ProfileMenuItem(
                icon: Icons.feedback_outlined,
                title: 'Feedback',
                subtitle: 'Tell us your suggestions',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FeedbackScreen(),
                    ),
                  );
                },
              ),

            ],
          ),
          
          const SizedBox(height: 32),
        ],
      ),
        );
      },
    );
  }

  Widget _buildMenuGroup({
    required String title,
    required List<ProfileMenuItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: AppTextStyles.subtitle2.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  /// 打开隐私协议
  Future<void> _openPrivacyPolicy() async {
    try {
      final uri = Uri.parse(AgreementService.privacyPolicyUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showMessage('Cannot open the privacy policy link. Please check your internet connection.');
      }
    } catch (e) {
      _showMessage('Failed to open privacy policy: $e');
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
