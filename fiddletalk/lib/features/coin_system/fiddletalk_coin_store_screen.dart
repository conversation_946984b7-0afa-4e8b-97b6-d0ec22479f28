import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/models/fiddletalk_store_item_model.dart';
import '../../core/services/in_app_purchase_service.dart';
import '../../core/services/fiddletalk_in_app_purchase_service.dart';

/// FiddleTalk金币商城页面
class FiddleTalkCoinStoreScreen extends StatefulWidget {
  const FiddleTalkCoinStoreScreen({super.key});

  @override
  State<FiddleTalkCoinStoreScreen> createState() => _FiddleTalkCoinStoreScreenState();
}

class _FiddleTalkCoinStoreScreenState extends State<FiddleTalkCoinStoreScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _gradientController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  final InAppPurchaseService _purchaseService = InAppPurchaseService();
  final FiddleTalkInAppPurchaseService _fiddletalkIapService = FiddleTalkInAppPurchaseService();
  
  bool _fiddletalkIsLoading = false;
  bool _fiddletalkIsInitialized = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeFiddleTalkStore();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _gradientController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  Future<void> _initializeFiddleTalkStore() async {
    setState(() {
      _fiddletalkIsLoading = true;
    });

    try {
      // 初始化FiddleTalk内购服务
      await _fiddletalkIapService.initializeFiddleTalk();
      
      // 设置回调
      _fiddletalkIapService.setFiddleTalkOnLoadingStateChanged((fiddletalkIsLoading) {
        if (mounted) {
          setState(() {
            _fiddletalkIsLoading = fiddletalkIsLoading;
          });
        }
      });

      _fiddletalkIapService.setFiddleTalkOnPurchaseResult((fiddletalkMessage, fiddletalkIsSuccess) {
        if (mounted) {
          _showFiddleTalkResultDialog(fiddletalkMessage, fiddletalkIsSuccess);
        }
      });

      // 查询所有FiddleTalk商品
      final fiddletalkAllItems = FiddleTalkStoreItemModel.getAllItems();
      final fiddletalkProductIds = fiddletalkAllItems.map((fiddletalkItem) => fiddletalkItem.code).toSet();
      
      await _fiddletalkIapService.queryFiddleTalkProductDetails(fiddletalkProductIds);
      
      setState(() {
        _fiddletalkIsInitialized = true;
        _fiddletalkIsLoading = false;
      });
    } catch (e) {
      setState(() {
        _fiddletalkIsLoading = false;
      });
      _showFiddleTalkResultDialog('Failed to initialize store: $e', false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _gradientController.dispose();
    _fiddletalkIapService.disposeFiddleTalk();
    super.dispose();
  }

  Future<void> _purchaseFiddleTalkItem(FiddleTalkStoreItemModel fiddletalkItem) async {
    if (!_fiddletalkIsInitialized || !_fiddletalkIapService.fiddletalkStoreAvailable) {
      _showFiddleTalkResultDialog('FiddleTalk Store is not available', false);
      return;
    }

    if (_fiddletalkIapService.fiddletalkIsProcessing) {
      debugPrint('FiddleTalkStore: Purchase already in progress, ignoring duplicate request');
      return;
    }

    // 开始FiddleTalk内购流程
    await _fiddletalkIapService.buyFiddleTalkProduct(fiddletalkItem.code);
  }

  void _showFiddleTalkResultDialog(String fiddletalkMessage, bool fiddletalkIsSuccess) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: AlertDialog(
            backgroundColor: Colors.transparent,
            content: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.95),
                    Colors.white.withOpacity(0.9),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: fiddletalkIsSuccess ? AppColors.accent.withOpacity(0.3) : Colors.red.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: const Offset(0, 8),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: (fiddletalkIsSuccess ? AppColors.accent : Colors.red).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      fiddletalkIsSuccess ? Icons.check_circle : Icons.error,
                      size: 48,
                      color: fiddletalkIsSuccess ? AppColors.accent : Colors.red,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    fiddletalkIsSuccess ? 'Purchase Success!' : 'Purchase Failed',
                    style: AppTextStyles.headline6.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    fiddletalkMessage,
                    style: AppTextStyles.body1.copyWith(
                      color: Colors.black54,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: fiddletalkIsSuccess ? AppColors.accent : Colors.red,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: const Text('OK'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          Column(
            children: [
              _buildHeader(),
              Expanded(
                child: AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value,
                      child: _buildStoreContent(),
                    );
                  },
                ),
              ),
            ],
          ),
          if (_fiddletalkIsLoading) _buildFiddleTalkLoadingOverlay(),
        ],
      ),
    );
  }

  Widget _buildFiddleTalkLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.6),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                offset: const Offset(0, 4),
                blurRadius: 12,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                strokeWidth: 3,
              ),
              const SizedBox(height: 16),
              Text(
                'Processing Purchase...',
                style: AppTextStyles.subtitle1.copyWith(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please wait',
                style: AppTextStyles.body2.copyWith(
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1,
            ),
          ),
          child: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: 20,
          ),
        ),
        onPressed: _fiddletalkIsLoading ? null : () => Navigator.of(context).pop(),
      ),
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  Widget _buildHeader() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.18; // 减少头部高度到18%
    
    return AnimatedBuilder(
      animation: _gradientController,
      builder: (context, child) {
        return Container(
          height: headerHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withOpacity(0.8),
                AppColors.accent.withOpacity(0.6),
              ],
              stops: [
                0.0,
                0.5 + 0.3 * (_gradientController.value),
                1.0,
              ],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(24),
              bottomRight: Radius.circular(24),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.monetization_on,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Coin Store',
                    style: AppTextStyles.headline6.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildCurrentBalance(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentBalance() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.account_balance_wallet,
            color: Colors.white,
            size: 14,
          ),
          const SizedBox(width: 6),
          Text(
            'Balance: ${_purchaseService.currentCoins}',
            style: AppTextStyles.caption.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 12,
              shadows: [
                Shadow(
                  offset: const Offset(0, 1),
                  blurRadius: 2,
                  color: Colors.black.withOpacity(0.3),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreContent() {
    final fiddletalkAllItems = FiddleTalkStoreItemModel.getAllItems();
    final fiddletalkRegularItems = fiddletalkAllItems.where((fiddletalkItem) => !fiddletalkItem.isPromotion).toList();
    final fiddletalkPromotionItems = fiddletalkAllItems.where((fiddletalkItem) => fiddletalkItem.isPromotion).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 普通商品区域
          if (fiddletalkRegularItems.isNotEmpty) ...[
            _buildSectionHeader('Coin Packages', 'Standard packages for your needs'),
            const SizedBox(height: 16),
            _buildItemsGrid(fiddletalkRegularItems),
          ],
          
          // 促销商品区域
          if (fiddletalkPromotionItems.isNotEmpty) ...[
            const SizedBox(height: 32),
            _buildSectionHeader('Special Offers', 'Limited time promotional deals'),
            const SizedBox(height: 16),
            _buildItemsGrid(fiddletalkPromotionItems),
          ],
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, String subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: AppTextStyles.body2.copyWith(
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  Widget _buildItemsGrid(List<FiddleTalkStoreItemModel> fiddletalkItems) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: fiddletalkItems.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildStoreItemCard(fiddletalkItems[index]),
        );
      },
    );
  }

  Widget _buildStoreItemCard(FiddleTalkStoreItemModel fiddletalkItem) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        border: Border.all(
          color: fiddletalkItem.isPromotion 
              ? AppColors.accent.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // 左侧图标
          _buildItemIcon(fiddletalkItem),
          const SizedBox(width: 16),
          
          // 中间信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (fiddletalkItem.isPromotion) 
                  Padding(
                    padding: const EdgeInsets.only(bottom: 6),
                    child: _buildPromotionBadge(fiddletalkItem),
                  ),
                _buildItemTitle(fiddletalkItem),
                const SizedBox(height: 4),
                _buildItemCoins(fiddletalkItem),
              ],
            ),
          ),
          
          // 右侧价格和按钮
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildItemPrice(fiddletalkItem),
              const SizedBox(height: 12),
              _buildPurchaseButton(fiddletalkItem),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionBadge(FiddleTalkStoreItemModel fiddletalkItem) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: AppColors.accent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        fiddletalkItem.tags ?? 'DEAL',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildItemIcon(FiddleTalkStoreItemModel fiddletalkItem) {
    return Container(
      width: 52,
      height: 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            (fiddletalkItem.isPromotion ? AppColors.accent : AppColors.primary).withOpacity(0.1),
            (fiddletalkItem.isPromotion ? AppColors.accent : AppColors.primary).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (fiddletalkItem.isPromotion ? AppColors.accent : AppColors.primary).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Icon(
        Icons.monetization_on,
        color: fiddletalkItem.isPromotion ? AppColors.accent : AppColors.primary,
        size: 28,
      ),
    );
  }

  Widget _buildItemTitle(FiddleTalkStoreItemModel fiddletalkItem) {
    return Text(
      fiddletalkItem.displayName,
      style: AppTextStyles.subtitle1.copyWith(
        fontWeight: FontWeight.bold,
        color: Colors.black87,
        fontSize: 16,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildItemCoins(FiddleTalkStoreItemModel fiddletalkItem) {
    return Row(
      children: [
        Icon(
          Icons.stars,
          size: 14,
          color: AppColors.accent,
        ),
        const SizedBox(width: 4),
        Text(
          '${fiddletalkItem.exchangeCoin} coins',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.accent,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildItemPrice(FiddleTalkStoreItemModel fiddletalkItem) {
    return Text(
      '\$${fiddletalkItem.price.toStringAsFixed(2)}',
      style: AppTextStyles.headline6.copyWith(
        fontWeight: FontWeight.bold,
        color: Colors.black87,
        fontSize: 18,
      ),
    );
  }

  Widget _buildPurchaseButton(FiddleTalkStoreItemModel fiddletalkItem) {
    return SizedBox(
      width: 80,
      height: 36,
      child: ElevatedButton(
        onPressed: (_fiddletalkIsLoading || _fiddletalkIapService.fiddletalkIsProcessing) ? null : () => _purchaseFiddleTalkItem(fiddletalkItem),
        style: ElevatedButton.styleFrom(
          backgroundColor: fiddletalkItem.isPromotion ? AppColors.accent : AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          'Buy',
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
} 