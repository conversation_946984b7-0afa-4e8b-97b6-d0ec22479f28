import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/widgets/dynamic_card.dart';
import '../../shared/widgets/topic_card.dart';
import '../../shared/widgets/floating_create_button.dart';

/// Community Screen - Violin Players Forum
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  late TabController _tabController;
  final RefreshController _refreshController = RefreshController();
  final RefreshController _topicsRefreshController = RefreshController();
  final RefreshController _highlightsRefreshController = RefreshController();
  
  List<String> _dynamics = []; // Dynamic data
  List<String> _topics = []; // Topic data
  bool _isLoading = false;

  final List<String> _tabs = ['Latest', 'Hot Topics', 'Featured'];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    _topicsRefreshController.dispose();
    _highlightsRefreshController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Simulate loading data
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _dynamics = List.generate(15, (index) => 'Dynamic $index');
      _topics = List.generate(10, (index) => 'Topic $index');
      _isLoading = false;
    });
  }

  Future<void> _onRefresh() async {
    await _loadInitialData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading() async {
    // Simulate loading more data
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      if (_tabController.index == 0) {
        _dynamics.addAll(List.generate(10, (index) => 'Dynamic ${_dynamics.length + index}'));
      } else if (_tabController.index == 1) {
        _topics.addAll(List.generate(5, (index) => 'Topic ${_topics.length + index}'));
      }
    });
    
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            _buildTabBar(),
            Expanded(
              child: _buildTabBarView(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingCreateButton(
        onPressed: () {
          _showCreatePostBottomSheet();
        },
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Community',
                  style: AppTextStyles.headline6.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Share experiences, exchange techniques',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Search button
          IconButton(
            onPressed: () {
              // TODO: Open search page
            },
            icon: const Icon(
              Icons.search,
              color: AppColors.textSecondary,
            ),
          ),

          // Message button
          IconButton(
            onPressed: () {
              // TODO: Open message page
            },
            icon: const Icon(
              Icons.message_outlined,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: AppTextStyles.subtitle2.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.subtitle2,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
      ),
    );
  }

  Widget _buildTabBarView() {
    if (_isLoading && _dynamics.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildDynamicsList(),
        _buildTopicsList(),
        _buildHighlightsList(),
      ],
    );
  }

  Widget _buildDynamicsList() {
    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      footer: const ClassicFooter(
        textStyle: AppTextStyles.caption,
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _dynamics.length,
        itemBuilder: (context, index) {
          return DynamicCard(
            avatar: 'https://via.placeholder.com/40',
            username: 'Player${index + 1}',
            timeAgo: '${index + 1}m ago',
            content: 'Today I practiced Kreutzer Etude No.${index + 1}, found that slow practice really helps with rhythm control! Sharing my practice insights...',
            images: index % 3 == 0 ? ['https://via.placeholder.com/200'] : [],
            tags: ['#VibratoTechnique', '#PracticeTips'],
            likesCount: (index + 1) * 8,
            commentsCount: (index + 1) * 3,
            isLiked: index % 4 == 0,
            onTap: () {
              // TODO: Navigate to dynamic detail page
            },
            onLike: () {
              // TODO: Like functionality
            },
            onComment: () {
              // TODO: Comment functionality
            },
            onShare: () {
              // TODO: Share functionality
            },
          );
        },
      ),
    );
  }

  Widget _buildTopicsList() {
    return SmartRefresher(
      controller: _topicsRefreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      footer: const ClassicFooter(
        textStyle: AppTextStyles.caption,
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _topics.length,
        itemBuilder: (context, index) {
          return TopicCard(
            title: '#${AppConstants.topicTags[index % AppConstants.topicTags.length]}',
            description: 'Share your ${AppConstants.topicTags[index % AppConstants.topicTags.length]} experience and insights',
            participantsCount: (index + 1) * 50,
            postsCount: (index + 1) * 20,
            isHot: index % 3 == 0,
            onTap: () {
              // TODO: Navigate to topic detail page
            },
          );
        },
      ),
    );
  }

  Widget _buildHighlightsList() {
    return SmartRefresher(
      controller: _highlightsRefreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      footer: const ClassicFooter(
        textStyle: AppTextStyles.caption,
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _dynamics.length,
        itemBuilder: (context, index) {
          return DynamicCard(
            avatar: 'https://via.placeholder.com/40',
            username: 'Player${index + 1}',
            timeAgo: '${index + 1}h ago',
            content: 'Featured content: Advanced violin technique discussion about ${AppConstants.topicTags[index % AppConstants.topicTags.length]}...',
            images: index % 2 == 0 ? ['https://via.placeholder.com/200'] : [],
            tags: ['#Featured', '#${AppConstants.topicTags[index % AppConstants.topicTags.length]}'],
            likesCount: (index + 1) * 15,
            commentsCount: (index + 1) * 8,
            isLiked: index % 3 == 0,
            onTap: () {
              // TODO: Navigate to featured content detail page
            },
            onLike: () {
              // TODO: Like functionality
            },
            onComment: () {
              // TODO: Comment functionality
            },
            onShare: () {
              // TODO: Share functionality
            },
          );
        },
      ),
    );
  }

  void _showCreatePostBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Top drag indicator
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.divider,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Text(
                    'Create Content',
                    style: AppTextStyles.headline6.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Create options
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildCreateOption(
                    icon: Icons.article_outlined,
                    title: 'Create Post',
                    subtitle: 'Share techniques and tips with images',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to create post page
                    },
                  ),

                  _buildCreateOption(
                    icon: Icons.chat_bubble_outline,
                    title: 'Share Update',
                    subtitle: 'Quick share thoughts and moments',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to share update page
                    },
                  ),

                  _buildCreateOption(
                    icon: Icons.topic_outlined,
                    title: 'Create Topic',
                    subtitle: 'Start discussion, invite players to join',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to create topic page
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.subtitle1.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.body2,
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.textTertiary,
      ),
      onTap: onTap,
    );
  }
}
