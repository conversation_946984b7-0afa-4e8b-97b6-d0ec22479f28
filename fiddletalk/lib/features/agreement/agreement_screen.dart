import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/agreement_service.dart';
import '../main/main_screen.dart';

/// 用户协议页面
/// 首次启动时显示，用户需要同意隐私协议和用户协议
class AgreementScreen extends StatefulWidget {
  const AgreementScreen({super.key});

  @override
  State<AgreementScreen> createState() => _AgreementScreenState();
}

class _AgreementScreenState extends State<AgreementScreen> {
  final AgreementService _agreementService = AgreementService();
  bool _privacyPolicyChecked = false;
  bool _userAgreementChecked = false;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(),
              
              // Logo or App Name
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary,
                      AppColors.accent,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.music_note,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 24),
              
              Text(
                'Welcome to FiddleTalk',
                style: AppTextStyles.headline4.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                'To continue using our app, please read and agree to our terms.',
                style: AppTextStyles.body1.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // Agreement Cards
              _buildAgreementCard(
                title: 'Privacy Policy',
                description: 'Learn how we protect your personal information and privacy.',
                isChecked: _privacyPolicyChecked,
                onCheckChanged: (value) {
                  setState(() {
                    _privacyPolicyChecked = value ?? false;
                  });
                },
                onReadTap: () => _openUrl(AgreementService.privacyPolicyUrl),
              ),
              
              const SizedBox(height: 16),
              
              _buildAgreementCard(
                title: 'User Agreement',
                description: 'Understand your rights and responsibilities as a user.',
                isChecked: _userAgreementChecked,
                onCheckChanged: (value) {
                  setState(() {
                    _userAgreementChecked = value ?? false;
                  });
                },
                onReadTap: () => _openUrl(AgreementService.userAgreementUrl),
              ),
              
              const Spacer(),
              
              // Agree Button
              Container(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _canProceed && !_isLoading ? _proceedToApp : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _canProceed 
                      ? AppColors.primary 
                      : AppColors.textTertiary,
                    foregroundColor: Colors.white,
                    elevation: _canProceed ? 8 : 0,
                    shadowColor: AppColors.primary.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Agree and Continue',
                        style: AppTextStyles.button.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              Text(
                'By agreeing, you accept our Privacy Policy and User Agreement.',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.textTertiary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementCard({
    required String title,
    required String description,
    required bool isChecked,
    required ValueChanged<bool?> onCheckChanged,
    required VoidCallback onReadTap,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isChecked ? AppColors.primary : AppColors.border,
          width: isChecked ? 2 : 1,
        ),
        boxShadow: isChecked 
          ? [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.1),
                offset: const Offset(0, 4),
                blurRadius: 12,
              ),
            ]
          : [
              BoxShadow(
                color: AppColors.shadowLight,
                offset: const Offset(0, 2),
                blurRadius: 8,
              ),
            ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Checkbox(
                value: isChecked,
                onChanged: onCheckChanged,
                activeColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.subtitle1.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              TextButton(
                onPressed: onReadTap,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: Text(
                  'Read',
                  style: AppTextStyles.button.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 48),
            child: Text(
              description,
              style: AppTextStyles.body2.copyWith(
                color: AppColors.textSecondary,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool get _canProceed => _privacyPolicyChecked && _userAgreementChecked;

  Future<void> _openUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        _showError('Cannot open the link. Please check your internet connection.');
      }
    } catch (e) {
      _showError('Failed to open link: $e');
    }
  }

  Future<void> _proceedToApp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 保存用户同意状态
      await _agreementService.setUserAgreedToTerms();

      // 导航到主页面
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const MainScreen(),
          ),
        );
      }
    } catch (e) {
      _showError('Failed to save agreement: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
} 