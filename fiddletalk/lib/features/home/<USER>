import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/user_model.dart';
import '../../shared/providers/global_data_provider.dart';
import '../../shared/providers/user_data_provider.dart';
import '../../shared/widgets/user_action_dialog.dart';
import 'dart:async';
import '../../shared/widgets/post_card.dart';
import '../../shared/widgets/category_filter.dart';
import '../post/post_detail_screen.dart';
import '../author/author_profile_screen.dart';
import '../post/create_post_screen.dart';

/// Home Screen - Violin Content Feed
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  final RefreshController _refreshController = RefreshController();
  final ScrollController _scrollController = ScrollController();
  
  String _selectedCategory = 'All';
  String _selectedMusicStyle = 'All';
  String _selectedPlayingLevel = 'All';

  List<PostModel> _posts = [];
  bool _isLoading = false;
  int _currentPage = 1;
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  final UserDataProvider _userDataProvider = UserDataProvider();
  StreamSubscription<PostModel>? _postUpdateSubscription;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupPostStateListener();
    _globalDataProvider.initialize();
    _globalDataProvider.addListener(_onGlobalDataChanged);
  }

  @override
  void dispose() {
    _postUpdateSubscription?.cancel();
    _globalDataProvider.removeListener(_onGlobalDataChanged);
    _refreshController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onGlobalDataChanged() {
    if (mounted) {
      setState(() {
        // Refresh UI when global data changes
      });
    }
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      setState(() {
        final index = _posts.indexWhere((post) => post.id == updatedPost.id);
        if (index != -1) {
          _posts[index] = updatedPost;
        }
      });
    });
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final posts = await _dataService.getPosts(
        page: 1,
        limit: 10,
        category: _selectedCategory == 'All' ? null : _selectedCategory,
        musicStyle: _selectedMusicStyle == 'All' ? null : _selectedMusicStyle,
        playingLevel: _selectedPlayingLevel == 'All' ? null : _selectedPlayingLevel,
      );

      setState(() {
        _posts = posts;
        _currentPage = 1;
        _isLoading = false;
      });

      // Initialize post states
      _postStateService.initializePosts(posts);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // TODO: Show error message
    }
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    await _loadInitialData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading() async {
    try {
      final newPosts = await _dataService.getPosts(
        page: _currentPage + 1,
        limit: 10,
        category: _selectedCategory == 'All' ? null : _selectedCategory,
        musicStyle: _selectedMusicStyle == 'All' ? null : _selectedMusicStyle,
        playingLevel: _selectedPlayingLevel == 'All' ? null : _selectedPlayingLevel,
      );

      if (newPosts.isNotEmpty) {
        setState(() {
          _posts.addAll(newPosts);
          _currentPage++;
        });
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  void _onCategoryChanged(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _onRefresh();
  }

  void _onMusicStyleChanged(String style) {
    setState(() {
      _selectedMusicStyle = style;
    });
    _onRefresh();
  }

  void _onPlayingLevelChanged(String level) {
    setState(() {
      _selectedPlayingLevel = level;
    });
    _onRefresh();
  }

  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  void _navigateToAuthorProfile(UserModel author) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AuthorProfileScreen(author: author),
      ),
    );
  }

  void _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreatePostScreen(),
      ),
    );

    // If a new post was created, refresh the feed
    if (result == true) {
      _onRefresh();
    }
  }

  void _toggleLike(PostModel post) {
    _postStateService.toggleLike(post.id);
  }

  void _toggleCollect(PostModel post) {
    _postStateService.toggleCollect(post.id);
  }

  void _sharePost(PostModel post) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  /// 切换关注状态
  void _toggleFollow(PostModel post) {
    if (post.author != null) {
      _globalDataProvider.toggleUserFollow(post.author!.id);
      setState(() {
        // Refresh UI to show updated follow status
      });

    }
  }

  /// 显示更多操作菜单
  void _showMoreActions(PostModel post) {
    if (post.author == null) return;

    showUserActionDialog(
      context: context,
      userName: post.author!.nickname,
      onReport: () => _reportUser(post.author!),
      onBlock: () => _blockUser(post.author!),
    );
  }

  /// 举报用户
  void _reportUser(UserModel user) {
    _globalDataProvider.reportUser(user.id, ['举报原因']);
    // 刷新帖子列表，过滤被举报用户的帖子
    setState(() {
      _posts = _globalDataProvider.filterPosts(_posts);
    });
  }

  /// 拉黑用户
  void _blockUser(UserModel user) {
    _globalDataProvider.blockUser(user.id);
    // 刷新帖子列表，过滤被拉黑用户的帖子
    setState(() {
      _posts = _globalDataProvider.filterPosts(_posts);
    });


  }

  Widget _buildWelcomeHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(20, MediaQuery.of(context).padding.top + 24, 20, 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withOpacity(0.8),
            AppColors.accent.withOpacity(0.6),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to FiddleTalk',
                      style: AppTextStyles.headline5.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 2),
                            blurRadius: 4,
                            color: Colors.black.withOpacity(0.3),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'Tap the button to create and share your violin posts',
                      style: AppTextStyles.body2.copyWith(
                        color: Colors.white.withOpacity(0.9),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: _navigateToCreatePost,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.add_circle_outline,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Set status bar color to match header
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primary,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          _buildWelcomeHeader(),
          _buildCategoryFilters(),
          Expanded(
            child: _buildPostsList(),
          ),
        ],
      ),
    );
  }



  Widget _buildCategoryFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Content categories
          CategoryFilter(
            title: 'Content Category',
            categories: ['All', ...AppConstants.contentCategories],
            selectedCategory: _selectedCategory,
            onCategoryChanged: _onCategoryChanged,
          ),

          const SizedBox(height: 16),

          // Music styles
          CategoryFilter(
            title: 'Music Style',
            categories: ['All', ...AppConstants.musicStyles],
            selectedCategory: _selectedMusicStyle,
            onCategoryChanged: _onMusicStyleChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildPostsList() {
    if (_isLoading && _posts.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      footer: const ClassicFooter(
        textStyle: AppTextStyles.caption,
      ),
      child: ListView.separated(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _posts.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final post = _posts[index];
          // 获取最新的帖子状态
          final currentState = _postStateService.getPostState(post.id) ?? post;
          return PostCard(
            title: currentState.title,
            content: currentState.content,
            author: currentState.author?.nickname ?? 'Unknown User',
            authorAvatar: currentState.author?.avatar,
            authorId: currentState.authorId,
            category: currentState.category,
            primaryImage: currentState.primaryImage,
            images: currentState.images,
            tags: currentState.tags,
            likesCount: currentState.likesCount,
            commentsCount: _globalDataProvider.getPostCommentsCount(currentState.id),
            collectionsCount: currentState.collectionsCount,
            isLiked: currentState.isLiked,
            isCollected: currentState.isCollected,
            isHighlighted: currentState.isHighlighted,
            isFollowed: _globalDataProvider.isUserFollowed(currentState.authorId),
            timeAgo: currentState.formattedCreatedAt,
            onTap: () {
              _navigateToPostDetail(currentState);
            },
            onLike: () {
              _toggleLike(currentState);
            },
            onComment: () {
              _navigateToPostDetail(currentState);
            },
            onCollect: () {
              _toggleCollect(currentState);
            },
            onShare: () {
              _sharePost(currentState);
            },
            onAuthorTap: () {
              _navigateToAuthorProfile(currentState.author!);
            },
            // 只有不是当前用户的帖子才显示关注和更多操作按钮
            onFollow: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () {
              _toggleFollow(currentState);
            },
            onMoreActions: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () {
              _showMoreActions(currentState);
            },
          );
        },
      ),
    );
  }
}
