import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/user_model.dart';
import '../../shared/providers/global_data_provider.dart';
import '../../shared/providers/user_data_provider.dart';
import '../../shared/widgets/user_action_dialog.dart';
import 'dart:async';
import '../../shared/widgets/post_card.dart';
import '../post/post_detail_screen.dart';
import '../author/author_profile_screen.dart';

/// Liked Posts Screen - Display user's liked posts
class LikedPostsScreen extends StatefulWidget {
  const LikedPostsScreen({super.key});

  @override
  State<LikedPostsScreen> createState() => _LikedPostsScreenState();
}

class _LikedPostsScreenState extends State<LikedPostsScreen> {
  final RefreshController _refreshController = RefreshController();
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  final UserDataProvider _userDataProvider = UserDataProvider();

  List<PostModel> _likedPosts = [];
  bool _isLoading = false;
  StreamSubscription<PostModel>? _postUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _loadLikedPosts();
    _setupPostStateListener();
    _globalDataProvider.initialize();
    _globalDataProvider.addListener(_onGlobalDataChanged);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _postUpdateSubscription?.cancel();
    _globalDataProvider.removeListener(_onGlobalDataChanged);
    super.dispose();
  }

  void _onGlobalDataChanged() {
    if (mounted) {
      setState(() {
        // Refresh UI when global data changes
      });
    }
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      setState(() {
        if (updatedPost.isLiked) {
          // Add or update liked post
          final index = _likedPosts.indexWhere((post) => post.id == updatedPost.id);
          if (index != -1) {
            _likedPosts[index] = updatedPost;
          } else {
            _likedPosts.insert(0, updatedPost);
          }
        } else {
          // Remove unliked post
          _likedPosts.removeWhere((post) => post.id == updatedPost.id);
        }
      });
    });
  }

  Future<void> _loadLikedPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 首先从PostStateService获取已喜欢的帖子
      final likedPosts = _postStateService.getLikedPosts();

      // 如果PostStateService中没有数据，从数据服务加载并初始化
      if (likedPosts.isEmpty) {
        final allPosts = await _dataService.getPosts(page: 1, limit: 50);
        _postStateService.initializePosts(allPosts);
        final updatedLikedPosts = _postStateService.getLikedPosts();

        // 过滤被拉黑和举报用户的帖子
        final filteredPosts = _globalDataProvider.filterPosts(updatedLikedPosts);

        setState(() {
          _likedPosts = filteredPosts;
          _isLoading = false;
        });
      } else {
        // 过滤被拉黑和举报用户的帖子
        final filteredPosts = _globalDataProvider.filterPosts(likedPosts);

        setState(() {
          _likedPosts = filteredPosts;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    await _loadLikedPosts();
    _refreshController.refreshCompleted();
  }

  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  void _navigateToAuthorProfile(PostModel post) {
    if (post.author != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AuthorProfileScreen(author: post.author!),
        ),
      );
    }
  }

  void _toggleLike(PostModel post) {
    _postStateService.toggleLike(post.id);
  }

  void _toggleCollect(PostModel post) {
    _postStateService.toggleCollect(post.id);
  }

  void _sharePost(PostModel post) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  /// 切换关注状态
  void _toggleFollow(PostModel post) {
    if (post.author != null) {
      _globalDataProvider.toggleUserFollow(post.author!.id);
      setState(() {
        // Refresh UI to show updated follow status
      });

    }
  }

  /// 显示更多操作菜单
  void _showMoreActions(PostModel post) {
    if (post.author == null) return;

    showUserActionDialog(
      context: context,
      userName: post.author!.nickname,
      onReport: () => _reportUser(post.author!),
      onBlock: () => _blockUser(post.author!),
    );
  }

  /// 举报用户
  void _reportUser(UserModel user) {
    _globalDataProvider.reportUser(user.id, ['举报原因']);
    // 刷新帖子列表，过滤被举报用户的帖子
    setState(() {
      _likedPosts = _globalDataProvider.filterPosts(_likedPosts);
    });
  }

  /// 拉黑用户
  void _blockUser(UserModel user) {
    _globalDataProvider.blockUser(user.id);
    // 刷新帖子列表，过滤被拉黑用户的帖子
    setState(() {
      _likedPosts = _globalDataProvider.filterPosts(_likedPosts);
    });


  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'Liked Posts',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textPrimary),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.surface,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    if (_likedPosts.isEmpty) {
      return _buildEmptyState();
    }

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      onRefresh: _onRefresh,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: _likedPosts.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final post = _likedPosts[index];
          // 获取最新的帖子状态
          final currentState = _postStateService.getPostState(post.id) ?? post;
          return PostCard(
            title: currentState.title,
            content: currentState.content,
            author: currentState.author?.nickname ?? 'Unknown User',
            authorAvatar: currentState.author?.avatar,
            authorId: currentState.authorId,
            category: currentState.category,
            primaryImage: currentState.primaryImage,
            images: currentState.images,
            tags: currentState.tags,
            likesCount: currentState.likesCount,
            commentsCount: _globalDataProvider.getPostCommentsCount(currentState.id),
            collectionsCount: currentState.collectionsCount,
            isLiked: currentState.isLiked,
            isCollected: currentState.isCollected,
            isHighlighted: currentState.isHighlighted,
            isFollowed: _globalDataProvider.isUserFollowed(currentState.authorId),
            timeAgo: currentState.formattedCreatedAt,
            onTap: () => _navigateToPostDetail(currentState),
            onLike: () => _toggleLike(currentState),
            onComment: () => _navigateToPostDetail(currentState),
            onCollect: () => _toggleCollect(currentState),
            onShare: () => _sharePost(currentState),
            onAuthorTap: () => _navigateToAuthorProfile(currentState),
            // 只有不是当前用户的帖子才显示关注和更多操作按钮
            onFollow: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () => _toggleFollow(currentState),
            onMoreActions: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () => _showMoreActions(currentState),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.favorite_outline,
            size: 80,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Liked Posts',
            style: AppTextStyles.headline6.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Posts you like will appear here',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Explore Posts',
              style: AppTextStyles.button,
            ),
          ),
        ],
      ),
    );
  }
}
