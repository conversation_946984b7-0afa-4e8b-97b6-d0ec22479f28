import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/post_model.dart';
import '../../shared/widgets/post_card.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../core/services/user_service.dart';
import '../../shared/providers/global_data_provider.dart';
import '../post/post_detail_screen.dart';
import 'dart:async';

/// Author Profile Screen - Display author's profile and posts
class AuthorProfileScreen extends StatefulWidget {
  final UserModel author;

  const AuthorProfileScreen({
    super.key,
    required this.author,
  });

  @override
  State<AuthorProfileScreen> createState() => _AuthorProfileScreenState();
}

class _AuthorProfileScreenState extends State<AuthorProfileScreen> {
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  // final UserService _userService = UserService();

  List<PostModel> _authorPosts = [];
  bool _isLoading = false;
  bool _isFollowing = false;
  bool _isCurrentUser = false;
  StreamSubscription<PostModel>? _postUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _isCurrentUser = false; // 简化实现
    _loadAuthorPosts();
    _checkFollowStatus();
    _setupPostStateListener();
    _globalDataProvider.addListener(_onGlobalDataChanged);
  }

  @override
  void dispose() {
    _postUpdateSubscription?.cancel();
    _globalDataProvider.removeListener(_onGlobalDataChanged);
    super.dispose();
  }

  void _onGlobalDataChanged() {
    if (mounted) {
      setState(() {
        // Refresh data when global state changes
      });
    }
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      if (mounted) {
        setState(() {
          final index = _authorPosts.indexWhere((post) => post.id == updatedPost.id);
          if (index != -1) {
            _authorPosts[index] = updatedPost;
          }
        });
      }
    });
  }

  Future<void> _loadAuthorPosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取所有帖子，不限制数量
      final posts = await _dataService.getPosts(limit: 1000);
      final authorPosts = posts.where((post) => post.authorId == widget.author.id).toList();

      if (mounted) {
        setState(() {
          _authorPosts = authorPosts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkFollowStatus() async {
    // Mock follow status check
    setState(() {
      _isFollowing = false; // Default to not following
    });
  }

  Future<void> _toggleFollow() async {
    setState(() {
      _isFollowing = !_isFollowing;
    });

    // Mock API call
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      extendBodyBehindAppBar: true,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          _buildSliverPostsList(),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    final imageUrl = widget.author.avatar;

    return SliverAppBar(
      expandedHeight: 400,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: imageUrl != null && imageUrl.isNotEmpty
            ? _buildHeaderImage(imageUrl)
            : _buildHeaderGradient(),
        title: Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
              // Avatar and basic info
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white.withOpacity(0.2),
                    backgroundImage: imageUrl != null && imageUrl.isNotEmpty
                        ? AssetImage(imageUrl)
                        : null,
                    child: imageUrl == null || imageUrl.isEmpty
                        ? const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 30,
                          )
                        : null,
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.author.nickname,
                          style: AppTextStyles.headline6.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 3,
                                color: Colors.black.withOpacity(0.5),
                              ),
                            ],
                          ),
                        ),

                        if (widget.author.bio != null && widget.author.bio!.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            widget.author.bio!,
                            style: AppTextStyles.caption.copyWith(
                              color: Colors.white.withOpacity(0.9),
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.5),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Stats
              Row(
                children: [
                  _buildStatItem('Posts', widget.author.postsCount.toString(), true),
                  const SizedBox(width: 24),
                  _buildStatItem('Followers', widget.author.followersCount.toString(), true),
                  const SizedBox(width: 24),
                  _buildStatItem('Following', widget.author.followingCount.toString(), true),
                ],
              ),

              const SizedBox(height: 12),

              // Follow button
              if (!_isCurrentUser) ...[
                SizedBox(
                  width: 120,
                  height: 32,
                  child: ElevatedButton(
                    onPressed: _toggleFollow,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFollowing ? AppColors.surface : AppColors.primary,
                      foregroundColor: _isFollowing ? AppColors.textPrimary : Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: _isFollowing ? BorderSide(color: AppColors.primary) : BorderSide.none,
                      ),
                    ),
                    child: Text(
                      _isFollowing ? 'Following' : 'Follow',
                      style: AppTextStyles.caption.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        ),
      ),
      iconTheme: const IconThemeData(
        color: Colors.white,
        shadows: [
          Shadow(
            offset: Offset(0, 1),
            blurRadius: 3,
            color: Colors.black54,
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderImage(String imageUrl) {
    if (imageUrl.startsWith('assets/')) {
      return Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            imageUrl,
            fit: BoxFit.cover,
          ),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return _buildHeaderGradient();
    }
  }

  Widget _buildHeaderGradient() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryDark,
          ],
        ),
      ),
    );
  }

  Widget _buildSliverPostsList() {
    if (_isLoading) {
      return const SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: CircularProgressIndicator(
              color: AppColors.primary,
            ),
          ),
        ),
      );
    }

    if (_authorPosts.isEmpty) {
      return SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              children: [
                Icon(
                  Icons.post_add,
                  size: 64,
                  color: AppColors.textSecondary.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No posts yet',
                  style: AppTextStyles.headline6.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'This author hasn\'t shared any posts yet.',
                  style: AppTextStyles.body2.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final post = _authorPosts[index];
          // 获取最新的帖子状态
          final currentState = _postStateService.getPostState(post.id) ?? post;

          return Padding(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: index == 0 ? 16 : 8,
              bottom: index == _authorPosts.length - 1 ? 16 : 8,
            ),
            child: PostCard(
              title: currentState.title,
              content: currentState.content,
              author: currentState.author?.nickname ?? 'Unknown User',
              authorAvatar: currentState.author?.avatar,
              authorId: currentState.authorId,
              category: currentState.category,
              primaryImage: currentState.primaryImage,
              images: currentState.images,
              tags: currentState.tags,
              likesCount: currentState.likesCount,
              commentsCount: _globalDataProvider.getPostCommentsCount(currentState.id),
              collectionsCount: currentState.collectionsCount,
              isLiked: currentState.isLiked,
              isCollected: currentState.isCollected,
              isHighlighted: currentState.isHighlighted,
              isFollowed: _globalDataProvider.isUserFollowed(currentState.authorId),
              timeAgo: currentState.formattedCreatedAt,
              onTap: () {
                _navigateToPostDetail(currentState);
              },
              onLike: () {
                _toggleLike(currentState);
              },
              onComment: () {
                _navigateToPostDetail(currentState);
              },
              onCollect: () {
                _toggleCollect(currentState);
              },
              onShare: () {
                _sharePost(currentState);
              },
              onAuthorTap: () {
                // 在作者主页中，点击作者头像不需要跳转
              },
              onFollow: () {
                _toggleFollow();
              },
              onMoreActions: () {
                _showMoreActions(currentState);
              },
            ),
          );
        },
        childCount: _authorPosts.length,
      ),
    );
  }

  Widget _buildStatItem(String label, String value, [bool isWhiteText = false]) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
            color: isWhiteText ? Colors.white : AppColors.textPrimary,
            shadows: isWhiteText ? [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 3,
                color: Colors.black.withOpacity(0.5),
              ),
            ] : null,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.caption.copyWith(
            color: isWhiteText ? Colors.white.withOpacity(0.9) : AppColors.textSecondary,
            shadows: isWhiteText ? [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 2,
                color: Colors.black.withOpacity(0.5),
              ),
            ] : null,
          ),
        ),
      ],
    );
  }

  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  void _toggleLike(PostModel post) {
    _postStateService.toggleLike(post.id);
  }

  void _toggleCollect(PostModel post) {
    _postStateService.toggleCollect(post.id);
  }

  void _sharePost(PostModel post) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  void _showMoreActions(PostModel post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 拖拽指示器
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.divider,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // 选项列表
              ListTile(
                leading: const Icon(Icons.report_outlined),
                title: const Text('Report'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Report functionality
                },
              ),

              ListTile(
                leading: const Icon(Icons.block_outlined),
                title: const Text('Block User'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Block functionality
                },
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}