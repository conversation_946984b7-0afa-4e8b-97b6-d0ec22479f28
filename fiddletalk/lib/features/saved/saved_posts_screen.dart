import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/user_model.dart';
import '../../shared/providers/global_data_provider.dart';
import '../../shared/providers/user_data_provider.dart';
import '../../shared/widgets/user_action_dialog.dart';
import 'dart:async';
import '../../shared/widgets/post_card.dart';
import '../post/post_detail_screen.dart';
import '../author/author_profile_screen.dart';

/// Saved Posts Screen - Display user's saved posts
class SavedPostsScreen extends StatefulWidget {
  const SavedPostsScreen({super.key});

  @override
  State<SavedPostsScreen> createState() => _SavedPostsScreenState();
}

class _SavedPostsScreenState extends State<SavedPostsScreen> {
  final RefreshController _refreshController = RefreshController();
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  final UserDataProvider _userDataProvider = UserDataProvider();

  List<PostModel> _savedPosts = [];
  bool _isLoading = false;
  StreamSubscription<PostModel>? _postUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _loadSavedPosts();
    _setupPostStateListener();
    _globalDataProvider.initialize();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _postUpdateSubscription?.cancel();
    super.dispose();
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      setState(() {
        if (updatedPost.isCollected) {
          // Add or update saved post
          final index = _savedPosts.indexWhere((post) => post.id == updatedPost.id);
          if (index != -1) {
            _savedPosts[index] = updatedPost;
          } else {
            _savedPosts.insert(0, updatedPost);
          }
        } else {
          // Remove unsaved post
          _savedPosts.removeWhere((post) => post.id == updatedPost.id);
        }
      });
    });
  }

  Future<void> _loadSavedPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 首先从PostStateService获取已收藏的帖子
      final savedPosts = _postStateService.getCollectedPosts();

      // 如果PostStateService中没有数据，从数据服务加载并初始化
      if (savedPosts.isEmpty) {
        final allPosts = await _dataService.getPosts(page: 1, limit: 50);
        _postStateService.initializePosts(allPosts);
        final updatedSavedPosts = _postStateService.getCollectedPosts();

        // 过滤被拉黑和举报用户的帖子
        final filteredPosts = _globalDataProvider.filterPosts(updatedSavedPosts);

        setState(() {
          _savedPosts = filteredPosts;
          _isLoading = false;
        });
      } else {
        // 过滤被拉黑和举报用户的帖子
        final filteredPosts = _globalDataProvider.filterPosts(savedPosts);

        setState(() {
          _savedPosts = filteredPosts;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    await _loadSavedPosts();
    _refreshController.refreshCompleted();
  }

  void _navigateToPostDetail(PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post),
      ),
    );
  }

  void _navigateToAuthorProfile(PostModel post) {
    if (post.author != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AuthorProfileScreen(author: post.author!),
        ),
      );
    }
  }

  void _toggleLike(PostModel post) {
    _postStateService.toggleLike(post.id);
  }

  void _toggleCollect(PostModel post) {
    _postStateService.toggleCollect(post.id);
  }

  void _sharePost(PostModel post) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  /// 切换关注状态
  void _toggleFollow(PostModel post) {
    if (post.author != null) {
      _globalDataProvider.toggleUserFollow(post.author!.id);
      setState(() {
        // Refresh UI to show updated follow status
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _globalDataProvider.isUserFollowed(post.author!.id)
                ? 'Following ${post.author!.nickname}'
                : 'Unfollowed ${post.author!.nickname}'
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// 显示更多操作菜单
  void _showMoreActions(PostModel post) {
    if (post.author == null) return;

    showUserActionDialog(
      context: context,
      userName: post.author!.nickname,
      onReport: () => _reportUser(post.author!),
      onBlock: () => _blockUser(post.author!),
    );
  }

  /// 举报用户
  void _reportUser(UserModel user) {
    _globalDataProvider.reportUser(user.id, ['举报原因']);
    // 刷新帖子列表，过滤被举报用户的帖子
    setState(() {
      _savedPosts = _globalDataProvider.filterPosts(_savedPosts);
    });
  }

  /// 拉黑用户
  void _blockUser(UserModel user) {
    _globalDataProvider.blockUser(user.id);
    // 刷新帖子列表，过滤被拉黑用户的帖子
    setState(() {
      _savedPosts = _globalDataProvider.filterPosts(_savedPosts);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已拉黑 ${user.nickname}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'Saved Posts',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textPrimary),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
        ),
      );
    }

    if (_savedPosts.isEmpty) {
      return _buildEmptyState();
    }

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      onRefresh: _onRefresh,
      header: const WaterDropHeader(
        waterDropColor: AppColors.primary,
      ),
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: _savedPosts.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final post = _savedPosts[index];
          // 获取最新的帖子状态
          final currentState = _postStateService.getPostState(post.id) ?? post;
          return PostCard(
            title: currentState.title,
            content: currentState.content,
            author: currentState.author?.nickname ?? 'Unknown User',
            authorAvatar: currentState.author?.avatar,
            authorId: currentState.authorId,
            category: currentState.category,
            primaryImage: currentState.primaryImage,
            images: currentState.images,
            tags: currentState.tags,
            likesCount: currentState.likesCount,
            commentsCount: _globalDataProvider.getPostCommentsCount(currentState.id),
            collectionsCount: currentState.collectionsCount,
            isLiked: currentState.isLiked,
            isCollected: currentState.isCollected,
            isHighlighted: currentState.isHighlighted,
            isFollowed: _globalDataProvider.isUserFollowed(currentState.authorId),
            timeAgo: currentState.formattedCreatedAt,
            onTap: () => _navigateToPostDetail(currentState),
            onLike: () => _toggleLike(currentState),
            onComment: () => _navigateToPostDetail(currentState),
            onCollect: () => _toggleCollect(currentState),
            onShare: () => _sharePost(currentState),
            onAuthorTap: () => _navigateToAuthorProfile(currentState),
            // 只有不是当前用户的帖子才显示关注和更多操作按钮
            onFollow: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () => _toggleFollow(currentState),
            onMoreActions: _userDataProvider.isCurrentUser(currentState.authorId) ? null : () => _showMoreActions(currentState),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.bookmark_outline,
            size: 80,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'No Saved Posts',
            style: AppTextStyles.headline6.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Posts you save will appear here',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Explore Posts',
              style: AppTextStyles.button,
            ),
          ),
        ],
      ),
    );
  }
}
