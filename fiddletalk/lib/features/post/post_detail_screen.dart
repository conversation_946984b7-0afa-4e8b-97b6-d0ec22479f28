import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/comment_model.dart';
import '../../core/services/mock_data_service.dart';
import '../../core/services/post_state_service.dart';
import '../../core/services/user_service.dart';
import '../../shared/providers/global_data_provider.dart';
import '../../shared/providers/user_data_provider.dart';
import '../author/author_profile_screen.dart';
import 'dart:async';

/// Post Detail Screen - Display full post content
class PostDetailScreen extends StatefulWidget {
  final PostModel post;

  const PostDetailScreen({
    super.key,
    required this.post,
  });

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  final MockDataService _dataService = MockDataService();
  final PostStateService _postStateService = PostStateService();
  final GlobalDataProvider _globalDataProvider = GlobalDataProvider();
  final UserService _userService = UserService();
  late PostModel _currentPost;

  List<CommentModel> _comments = [];
  bool _isLoadingComments = false;
  StreamSubscription<PostModel>? _postUpdateSubscription;

  // 评论输入相关
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  bool _isSubmittingComment = false;

  // 回复相关状态
  CommentModel? _replyingToComment; // 当前正在回复的评论
  String _replyPlaceholder = 'Add a comment...'; // 输入框占位符

  @override
  void initState() {
    super.initState();
    _currentPost = widget.post;
    _postStateService.updatePostState(_currentPost);
    _loadComments();
    _setupPostStateListener();
    _globalDataProvider.initialize();

    // 添加到阅览记录
    _addToBrowseHistory();

    // 监听输入框内容变化，用于更新发送按钮状态
    _commentController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _postUpdateSubscription?.cancel();
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  void _setupPostStateListener() {
    _postUpdateSubscription = _postStateService.postUpdates.listen((updatedPost) {
      if (updatedPost.id == widget.post.id) {
        setState(() {
          _currentPost = updatedPost;
        });
      }
    });
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoadingComments = true;
    });

    try {
      // 从全局数据提供者获取评论
      final comments = _globalDataProvider.getPostComments(widget.post.id);
      setState(() {
        _comments = comments;
        _isLoadingComments = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingComments = false;
      });
    }
  }

  void _toggleLike() {
    _postStateService.toggleLike(_currentPost.id);
  }

  void _toggleCollect() {
    _postStateService.toggleCollect(_currentPost.id);
  }

  /// 添加到阅览记录
  Future<void> _addToBrowseHistory() async {
    try {
      await _globalDataProvider.initialize();
      await _globalDataProvider.addToBrowseHistory(_currentPost.id);
    } catch (e) {
      print('Error adding to browse history: $e');
    }
  }

  /// 切换关注状态
  void _toggleFollow() {
    if (_currentPost.author != null) {
      _globalDataProvider.toggleUserFollow(_currentPost.author!.id);
      setState(() {
        // Refresh UI to show updated follow status
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _globalDataProvider.isUserFollowed(_currentPost.author!.id)
                ? 'Following ${_currentPost.author!.nickname}'
                : 'Unfollowed ${_currentPost.author!.nickname}'
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// 显示全屏图片
  void _showImageFullScreen(String imageUrl) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.black,
        pageBuilder: (context, animation, secondaryAnimation) {
          return GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: Hero(
                  tag: 'post_image_${_currentPost.id}',
                  child: InteractiveViewer(
                    child: imageUrl.startsWith('assets/') 
                      ? Image.asset(imageUrl, fit: BoxFit.contain)
                      : imageUrl.startsWith('/')
                        ? Image.file(File(imageUrl), fit: BoxFit.contain)
                        : Image.network(imageUrl, fit: BoxFit.contain),
                  ),
                ),
              ),
            ),
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  /// 提交评论或回复
  Future<void> _submitComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty || _isSubmittingComment) return;

    setState(() {
      _isSubmittingComment = true;
    });

    try {
      final userDataProvider = Provider.of<UserDataProvider>(context, listen: false);

      if (_replyingToComment != null) {
        // 添加回复
        await _addReply(content, _replyingToComment!);
      } else {
        // 添加普通评论
        final newComment = CommentModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          postId: widget.post.id,
          authorId: userDataProvider.currentUserId,
          authorName: userDataProvider.currentUserNickname,
          authorAvatar: userDataProvider.currentUserAvatar,
          content: content,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // 添加到全局数据提供者
        await _globalDataProvider.addComment(newComment);
        
        // 重新加载评论列表，确保数据一致性
        await _loadComments();
      }

      // 清理输入状态
      setState(() {
        _commentController.clear();
        _replyingToComment = null;
        _replyPlaceholder = 'Add a comment...';
      });

      // 更新帖子评论数（包括回复）
      _updatePostCommentCount();
      _postStateService.updatePostState(_currentPost);

      // 隐藏键盘
      _commentFocusNode.unfocus();

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment posted successfully.'),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Comment failed to post. $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmittingComment = false;
      });
    }
  }

  /// 添加回复
  Future<void> _addReply(String content, CommentModel parentComment) async {
    final userDataProvider = Provider.of<UserDataProvider>(context, listen: false);

    final newReply = CommentModel(
      id: '${parentComment.id}_reply_${DateTime.now().millisecondsSinceEpoch}',
      postId: widget.post.id,
      authorId: userDataProvider.currentUserId,
      authorName: userDataProvider.currentUserNickname,
      authorAvatar: userDataProvider.currentUserAvatar,
      content: content,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // 添加回复到父评论
    final updatedParent = parentComment.addReply(newReply);

    // 更新全局数据提供者
    await _globalDataProvider.updateComment(updatedParent);
    
    // 重新加载评论列表，确保数据一致性
    await _loadComments();
  }

  /// 更新帖子评论总数
  void _updatePostCommentCount() {
    int totalComments = _comments.length;
    // 计算所有回复数量
    for (final comment in _comments) {
      totalComments += comment.replies.length;
    }

    _currentPost = _currentPost.copyWith(
      commentsCount: totalComments,
    );
  }

  /// 开始回复评论
  void _startReply(CommentModel comment) {
    setState(() {
      _replyingToComment = comment;
      _replyPlaceholder = 'Reply to @${comment.authorName}...';
    });
    _commentFocusNode.requestFocus();
  }

  /// 取消回复
  void _cancelReply() {
    setState(() {
      _replyingToComment = null;
      _replyPlaceholder = 'Add a comment...';
    });
  }

  /// 在评论列表中更新回复评论
  void _updateReplyInComments(String replyId, CommentModel updatedReply) {
    for (int i = 0; i < _comments.length; i++) {
      final comment = _comments[i];
      final replyIndex = comment.replies.indexWhere((reply) => reply.id == replyId);
      if (replyIndex != -1) {
        // 找到了回复，更新父评论的replies列表
        final updatedReplies = List<CommentModel>.from(comment.replies);
        updatedReplies[replyIndex] = updatedReply;
        _comments[i] = comment.copyWith(replies: updatedReplies);
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                _buildSliverAppBar(),
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPostContent(),
                      _buildAuthorInfo(),
                      _buildActionButtons(),
                      _buildCommentSection(),
                      const SizedBox(height: 80), // 为底部输入框留出空间
                    ],
                  ),
                ),
              ],
            ),
          ),
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    final imageUrl = _currentPost.primaryImage ??
                    (_currentPost.images.isNotEmpty ? _currentPost.images.first : null);
    
    return SliverAppBar(
      expandedHeight: imageUrl != null ? 300 : 100,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: imageUrl != null
            ? _buildHeaderImage(imageUrl)
            : _buildHeaderGradient(),
      ),
    );
  }

  Widget _buildHeaderImage(String imageUrl) {
    if (imageUrl.startsWith('assets/')) {
      return GestureDetector(
        onTap: () => _showImageFullScreen(imageUrl),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Hero(
              tag: 'post_image_${_currentPost.id}',
              child: Image.asset(
                imageUrl,
                fit: BoxFit.cover,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } 
    // 检查是否是本地文件路径（用户上传的图片）
    else if (imageUrl.startsWith('/')) {
      return FutureBuilder<bool>(
        future: _checkImageExists(imageUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: AppColors.surfaceVariant,
              child: const Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.primary,
                ),
              ),
            );
          }

          if (snapshot.data == true) {
            return GestureDetector(
              onTap: () => _showImageFullScreen(imageUrl),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Hero(
                    tag: 'post_image_${_currentPost.id}',
                    child: Image.file(
                      File(imageUrl),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: AppColors.surfaceVariant,
                        child: const Center(
                          child: Icon(Icons.image_not_supported, size: 48),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.3),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          } else {
            debugPrint('Image file does not exist in post detail: $imageUrl');
            return Container(
              color: AppColors.surfaceVariant,
              child: const Center(
                child: Icon(Icons.image_not_supported, size: 48),
              ),
            );
          }
        },
      );
    }
    else {
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary,
              AppColors.primaryDark,
            ],
          ),
        ),
      );
    }
  }

  Widget _buildHeaderGradient() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryDark,
          ],
        ),
      ),
    );
  }

  Widget _buildPostContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category and badges
          Wrap(
            spacing: 8,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _currentPost.category,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (_currentPost.isHighlighted)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.accent,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'Featured',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Title
          Text(
            _currentPost.title,
            style: AppTextStyles.headline5.copyWith(
              fontWeight: FontWeight.bold,
              height: 1.3,
            ),
          ),

          const SizedBox(height: 16),

          // Content
          Text(
            _currentPost.content,
            style: AppTextStyles.body1.copyWith(
              height: 1.6,
            ),
          ),

          const SizedBox(height: 16),

          // Tags
          if (_currentPost.tags.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _currentPost.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    tag,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildAuthorInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              if (_currentPost.author != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AuthorProfileScreen(
                      author: _currentPost.author!,
                    ),
                  ),
                );
              }
            },
            child: CircleAvatar(
              radius: 24,
              backgroundColor: AppColors.primary.withOpacity(0.1),
              backgroundImage: _currentPost.author?.avatar != null && _currentPost.author!.avatar!.isNotEmpty
                  ? AssetImage(_currentPost.author!.avatar!)
                  : null,
              child: _currentPost.author?.avatar == null || _currentPost.author!.avatar!.isEmpty
                  ? const Icon(
                      Icons.person,
                      color: AppColors.primary,
                      size: 24,
                    )
                  : null,
            ),
          ),

          const SizedBox(width: 12),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    if (_currentPost.author != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AuthorProfileScreen(
                            author: _currentPost.author!,
                          ),
                        ),
                      );
                    }
                  },
                  child: Text(
                    _currentPost.author?.nickname ?? 'Unknown User',
                    style: AppTextStyles.subtitle1.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  _currentPost.formattedCreatedAt,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          _buildFollowButton(),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: _currentPost.isLiked ? Icons.favorite : Icons.favorite_border,
            label: _currentPost.likesCount.toString(),
            color: _currentPost.isLiked ? AppColors.error : AppColors.textSecondary,
            onTap: _toggleLike,
          ),
                      _buildActionButton(
              icon: Icons.chat_bubble_outline,
              label: _globalDataProvider.getPostCommentsCount(widget.post.id).toString(),
              color: AppColors.textSecondary,
              onTap: () {
              _commentFocusNode.requestFocus();
            },
          ),
          _buildActionButton(
            icon: _currentPost.isCollected ? Icons.bookmark : Icons.bookmark_border,
            label: _currentPost.collectionsCount.toString(),
            color: _currentPost.isCollected ? AppColors.accent : AppColors.textSecondary,
            onTap: _toggleCollect,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.caption.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comments list
          if (_isLoadingComments)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: CircularProgressIndicator(
                  color: AppColors.primary,
                ),
              ),
            )
          else if (_comments.isEmpty)
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 48,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No comments yet',
                      style: AppTextStyles.subtitle1.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Be the first to comment!',
                      style: AppTextStyles.body2.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _comments.length,
              separatorBuilder: (context, index) => const SizedBox(height: 16),
              itemBuilder: (context, index) {
                return _buildCommentItem(_comments[index]);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(CommentModel comment) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 1),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comment header
          Row(
            children: [
              // Author avatar
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary.withOpacity(0.1),
                backgroundImage: comment.authorAvatar != null && comment.authorAvatar!.isNotEmpty
                    ? AssetImage(comment.authorAvatar!)
                    : null,
                child: comment.authorAvatar == null || comment.authorAvatar!.isEmpty
                    ? const Icon(
                        Icons.person,
                        size: 16,
                        color: AppColors.primary,
                      )
                    : null,
              ),

              const SizedBox(width: 8),

              // Author name and time
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.authorName,
                      style: AppTextStyles.caption.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      comment.formattedCreatedAt,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textTertiary,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),

              // Like button
              GestureDetector(
                onTap: () => _toggleCommentLike(comment),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      comment.isLiked ? Icons.favorite : Icons.favorite_border,
                      size: 16,
                      color: comment.isLiked ? AppColors.error : AppColors.textTertiary,
                    ),
                    if (comment.likesCount > 0) ...[
                      const SizedBox(width: 4),
                      Text(
                        comment.likesCount.toString(),
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textTertiary,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Comment content
          Text(
            comment.content,
            style: AppTextStyles.body2.copyWith(
              height: 1.4,
            ),
          ),

          const SizedBox(height: 8),

          // Action buttons (Reply)
          Row(
            children: [
              GestureDetector(
                onTap: () => _startReply(comment),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.reply,
                      size: 14,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Reply',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textTertiary,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              if (comment.replies.isNotEmpty) ...[
                const SizedBox(width: 16),
                Text(
                  '${comment.replies.length} ${comment.replies.length == 1 ? 'reply' : 'replies'}',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textTertiary,
                    fontSize: 11,
                  ),
                ),
              ],
            ],
          ),

          // Replies
          if (comment.replies.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              margin: const EdgeInsets.only(left: 24),
              child: Column(
                children: comment.replies.map((reply) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          radius: 12,
                          backgroundColor: AppColors.primary.withOpacity(0.1),
                          backgroundImage: reply.authorAvatar != null && reply.authorAvatar!.isNotEmpty
                              ? AssetImage(reply.authorAvatar!)
                              : null,
                          child: reply.authorAvatar == null || reply.authorAvatar!.isEmpty
                              ? const Icon(
                                  Icons.person,
                                  size: 12,
                                  color: AppColors.primary,
                                )
                              : null,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    reply.authorName,
                                    style: AppTextStyles.caption.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 11,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    reply.formattedCreatedAt,
                                    style: AppTextStyles.caption.copyWith(
                                      color: AppColors.textTertiary,
                                      fontSize: 10,
                                    ),
                                  ),
                                  const Spacer(),
                                  // Reply like button
                                  GestureDetector(
                                    onTap: () => _toggleCommentLike(reply),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          reply.isLiked ? Icons.favorite : Icons.favorite_border,
                                          size: 12,
                                          color: reply.isLiked ? AppColors.error : AppColors.textTertiary,
                                        ),
                                        if (reply.likesCount > 0) ...[
                                          const SizedBox(width: 2),
                                          Text(
                                            reply.likesCount.toString(),
                                            style: AppTextStyles.caption.copyWith(
                                              color: AppColors.textTertiary,
                                              fontSize: 9,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 2),
                              Text(
                                reply.content,
                                style: AppTextStyles.caption.copyWith(
                                  height: 1.3,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建评论输入框
  Widget _buildCommentInput() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 回复状态提示
          if (_replyingToComment != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: AppColors.primary.withOpacity(0.1),
              child: Row(
                children: [
                  Icon(
                    Icons.reply,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Replying to @${_replyingToComment!.authorName}',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: _cancelReply,
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),

          // 输入框区域
          Container(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 12,
              bottom: 12 + MediaQuery.of(context).padding.bottom,
            ),
            child: Row(
        children: [
          // 用户头像
          Consumer<UserDataProvider>(
            builder: (context, userDataProvider, child) {
              final avatar = userDataProvider.currentUserAvatar;
              return CircleAvatar(
                radius: 18,
                backgroundColor: AppColors.primary.withOpacity(0.1),
                backgroundImage: avatar != null && avatar.isNotEmpty
                    ? (avatar.startsWith('assets/')
                        ? AssetImage(avatar)
                        : FileImage(File(avatar))) as ImageProvider
                    : null,
                child: avatar == null || avatar.isEmpty
                    ? const Icon(
                        Icons.person,
                        size: 20,
                        color: AppColors.primary,
                      )
                    : null,
              );
            },
          ),

          const SizedBox(width: 12),

          // 输入框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _commentFocusNode.hasFocus
                      ? AppColors.primary
                      : Colors.transparent,
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _commentController,
                focusNode: _commentFocusNode,
                decoration: InputDecoration(
                  hintText: _replyPlaceholder,
                  hintStyle: AppTextStyles.body2.copyWith(
                    color: AppColors.textTertiary,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
                style: AppTextStyles.body2,
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _submitComment(),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 发送按钮
          GestureDetector(
            onTap: _isSubmittingComment ? null : _submitComment,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _commentController.text.trim().isNotEmpty && !_isSubmittingComment
                    ? AppColors.primary
                    : AppColors.textTertiary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: _isSubmittingComment
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(
                      Icons.send,
                      color: Colors.white,
                      size: 16,
                    ),
            ),
          ),
        ],
      ),
    ),
        ],
      ),
    );
  }

  /// 构建关注按钮
  Widget _buildFollowButton() {
    if (_currentPost.author == null) {
      return const SizedBox.shrink();
    }

    // 如果是当前用户的帖子，不显示关注按钮
    final userDataProvider = Provider.of<UserDataProvider>(context, listen: false);
    if (userDataProvider.isCurrentUser(_currentPost.authorId)) {
      return const SizedBox.shrink();
    }

    final isFollowed = _globalDataProvider.isUserFollowed(_currentPost.author!.id);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: TextButton.icon(
        onPressed: _toggleFollow,
        icon: isFollowed
            ? const Icon(Icons.check, size: 16)
            : const Icon(Icons.add, size: 16),
        label: Text(
          isFollowed ? 'Following' : 'Follow',
          style: AppTextStyles.button.copyWith(
            color: isFollowed ? AppColors.textSecondary : AppColors.primary,
          ),
        ),
        style: TextButton.styleFrom(
          foregroundColor: isFollowed ? AppColors.textSecondary : AppColors.primary,
          backgroundColor: isFollowed
              ? AppColors.surfaceVariant
              : AppColors.primary.withOpacity(0.1),
          side: BorderSide(
            color: isFollowed ? AppColors.border : AppColors.primary,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ),
    );
  }

  // 防止重复点击的评论ID集合
  final Set<String> _processingCommentLikes = {};

  // 检查图片文件是否存在
  Future<bool> _checkImageExists(String imagePath) async {
    try {
      final file = File(imagePath);
      return await file.exists();
    } catch (e) {
      debugPrint('Error checking image existence: $e');
      return false;
    }
  }

  /// 切换评论点赞状态
  Future<void> _toggleCommentLike(CommentModel comment) async {
    // 防止重复点击
    if (_processingCommentLikes.contains(comment.id)) {
      debugPrint('Comment like already processing: ${comment.id}');
      return;
    }

    _processingCommentLikes.add(comment.id);

    try {
      final updatedComment = comment.copyWith(
        isLiked: !comment.isLiked,
        likesCount: comment.isLiked
            ? comment.likesCount - 1
            : comment.likesCount + 1,
      );

      // 先更新本地状态以提供即时反馈
      setState(() {
        // 检查是否是主评论
        final index = _comments.indexWhere((c) => c.id == comment.id);
        if (index != -1) {
          // 是主评论，直接更新
          _comments[index] = updatedComment;
          debugPrint('Updated main comment UI: ${comment.id}, isLiked: ${updatedComment.isLiked}');
        } else {
          // 可能是回复评论，需要在父评论的replies中查找并更新
          bool found = false;
          for (int i = 0; i < _comments.length; i++) {
            final parentComment = _comments[i];
            final replyIndex = parentComment.replies.indexWhere((reply) => reply.id == comment.id);
            if (replyIndex != -1) {
              // 找到了回复，更新父评论的replies列表
              final updatedReplies = List<CommentModel>.from(parentComment.replies);
              updatedReplies[replyIndex] = updatedComment;
              _comments[i] = parentComment.copyWith(replies: updatedReplies);
              found = true;
              debugPrint('Updated reply comment UI: ${comment.id}, isLiked: ${updatedComment.isLiked}, parent: ${parentComment.id}');
              break;
            }
          }
          if (!found) {
            debugPrint('Warning: Could not find comment to update in UI: ${comment.id}');
          }
        }
      });

      // 更新全局数据
      await _globalDataProvider.updateComment(updatedComment);

      debugPrint('Comment like updated successfully: ${comment.id}');
    } catch (e) {
      debugPrint('Error updating comment like: $e');

      // 如果更新失败，回滚本地状态
      setState(() {
        final index = _comments.indexWhere((c) => c.id == comment.id);
        if (index != -1) {
          // 是主评论，直接回滚
          _comments[index] = comment; // 恢复原始状态
        } else {
          // 可能是回复评论，需要在父评论的replies中回滚
          _updateReplyInComments(comment.id, comment);
        }
      });

      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('点赞操作失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      _processingCommentLikes.remove(comment.id);
    }
  }
}
