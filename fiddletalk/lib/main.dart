import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'shared/models/user_model.dart';
import 'shared/models/post_model.dart';
import 'shared/models/comment_model.dart';
import 'shared/models/achievement_model.dart';
import 'shared/providers/user_data_provider.dart';
import 'core/services/data_validation_service.dart';
import 'features/splash/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化Hive数据库
  await Hive.initFlutter();

  // 注册Hive适配器
  Hive.registerAdapter(UserModelAdapter());
  Hive.registerAdapter(PostModelAdapter());
  Hive.registerAdapter(CommentModelAdapter());
  Hive.registerAdapter(AchievementModelAdapter());
  Hive.registerAdapter(AchievementTypeAdapter());

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 启动数据验证和清理
  final dataValidationService = DataValidationService();
  dataValidationService.validateAndCleanupData();

  runApp(const FiddleTalkApp());
}

class FiddleTalkApp extends StatelessWidget {
  const FiddleTalkApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) {
            final provider = UserDataProvider();
            // Initialize user data when the provider is created
            provider.initialize();
            return provider;
          },
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        home: const SplashScreen(),
        // 这里将添加路由配置
      ),
    );
  }
}


