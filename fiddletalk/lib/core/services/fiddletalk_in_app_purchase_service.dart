import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/src/types/app_store_purchase_param.dart';
import '../../shared/models/fiddletalk_store_item_model.dart';
import 'in_app_purchase_service.dart';

/// FiddleTalk真实内购服务类
class FiddleTalkInAppPurchaseService {
  static final FiddleTalkInAppPurchaseService _instance = FiddleTalkInAppPurchaseService._internal();
  factory FiddleTalkInAppPurchaseService() => _instance;
  FiddleTalkInAppPurchaseService._internal();

  final InAppPurchase _fiddletalkInAppPurchase = InAppPurchase.instance;
  final InAppPurchaseService _fiddletalkCoinService = InAppPurchaseService();
  
  late StreamSubscription<List<PurchaseDetails>> _fiddletalkPurchaseSubscription;
  bool _fiddletalkStoreAvailable = false;
  Map<String, ProductDetails> _fiddletalkProducts = {};
  
  // 购买状态回调
  Function(String message, bool isSuccess)? _fiddletalkOnPurchaseResult;
  Function(bool isLoading)? _fiddletalkOnLoadingStateChanged;
  
  bool _fiddletalkIsProcessing = false;

  /// 初始化FiddleTalk内购服务
  Future<void> initializeFiddleTalk() async {
    try {
      _fiddletalkStoreAvailable = await _fiddletalkInAppPurchase.isAvailable();
      if (!_fiddletalkStoreAvailable) {
        debugPrint('FiddleTalkInAppPurchase: Store not available');
        throw Exception('App Store is not available');
      }

      // 监听购买状态变化
      _fiddletalkPurchaseSubscription = _fiddletalkInAppPurchase.purchaseStream.listen(
        _fiddletalkOnPurchaseUpdated,
        onDone: () => _fiddletalkPurchaseSubscription.cancel(),
        onError: (error) {
          debugPrint('FiddleTalkInAppPurchase Error: $error');
          _fiddletalkOnLoadingStateChanged?.call(false);
          _fiddletalkIsProcessing = false;
          _fiddletalkOnPurchaseResult?.call('Purchase stream error: $error', false);
        },
      );

      debugPrint('FiddleTalkInAppPurchase: Service initialized successfully');
    } catch (e) {
      debugPrint('FiddleTalkInAppPurchase: Failed to initialize: $e');
      _fiddletalkOnLoadingStateChanged?.call(false);
      _fiddletalkIsProcessing = false;
      throw e;
    }
  }

  /// 查询FiddleTalk商品详情
  Future<bool> queryFiddleTalkProductDetails(Set<String> fiddletalkProductIds) async {
    if (!_fiddletalkStoreAvailable) {
      debugPrint('FiddleTalkInAppPurchase: Store not available for product query');
      return false;
    }

    try {
      _fiddletalkOnLoadingStateChanged?.call(true);
      
      final ProductDetailsResponse fiddletalkResponse = await _fiddletalkInAppPurchase.queryProductDetails(fiddletalkProductIds);
      
      if (fiddletalkResponse.notFoundIDs.isNotEmpty) {
        debugPrint('FiddleTalkInAppPurchase: Products not found: ${fiddletalkResponse.notFoundIDs}');
      }

      _fiddletalkProducts.clear();
      for (ProductDetails fiddletalkProduct in fiddletalkResponse.productDetails) {
        _fiddletalkProducts[fiddletalkProduct.id] = fiddletalkProduct;
        debugPrint('FiddleTalkInAppPurchase: Found product: ${fiddletalkProduct.id} - ${fiddletalkProduct.title}');
      }

      _fiddletalkOnLoadingStateChanged?.call(false);
      return fiddletalkResponse.productDetails.isNotEmpty;
    } catch (e) {
      _fiddletalkOnLoadingStateChanged?.call(false);
      debugPrint('FiddleTalkInAppPurchase: Query products failed: $e');
      return false;
    }
  }

  /// 购买FiddleTalk商品
  Future<void> buyFiddleTalkProduct(String fiddletalkProductId) async {
    if (_fiddletalkIsProcessing) {
      debugPrint('FiddleTalkInAppPurchase: Purchase already in progress');
      return;
    }

    if (!_fiddletalkStoreAvailable) {
      _fiddletalkOnPurchaseResult?.call('App Store is not available', false);
      return;
    }

    final ProductDetails? fiddletalkProduct = _fiddletalkProducts[fiddletalkProductId];
    if (fiddletalkProduct == null) {
      _fiddletalkOnPurchaseResult?.call('Product not found: $fiddletalkProductId', false);
      return;
    }

    try {
      _fiddletalkIsProcessing = true;
      _fiddletalkOnLoadingStateChanged?.call(true);
      
      bool fiddletalkPurchaseResult = false;
      
      if (Platform.isIOS) {
        final AppStorePurchaseParam fiddletalkPurchaseParam = AppStorePurchaseParam(
          productDetails: fiddletalkProduct,
          applicationUserName: "fiddle_talk",
          quantity: 1,
        );

        fiddletalkPurchaseResult = await _fiddletalkInAppPurchase.buyConsumable(
          purchaseParam: fiddletalkPurchaseParam,
          autoConsume: true,
        );
      } else {
        final PurchaseParam fiddletalkPurchaseParam = PurchaseParam(
          productDetails: fiddletalkProduct,
        );

        fiddletalkPurchaseResult = await _fiddletalkInAppPurchase.buyConsumable(
          purchaseParam: fiddletalkPurchaseParam,
          autoConsume: true,
        );
      }

      if (!fiddletalkPurchaseResult) {
        _fiddletalkIsProcessing = false;
        _fiddletalkOnLoadingStateChanged?.call(false);
        _fiddletalkOnPurchaseResult?.call('Failed to initiate purchase', false);
      }
      // 如果成功发起购买，loading状态由购买回调处理
    } catch (e) {
      _fiddletalkIsProcessing = false;
      _fiddletalkOnLoadingStateChanged?.call(false);
      _fiddletalkOnPurchaseResult?.call('Purchase failed: $e', false);
      debugPrint('FiddleTalkInAppPurchase: Buy product failed: $e');
    }
  }

  /// 处理FiddleTalk购买状态更新
  void _fiddletalkOnPurchaseUpdated(List<PurchaseDetails> fiddletalkPurchaseDetailsList) async {
    for (PurchaseDetails fiddletalkPurchaseDetails in fiddletalkPurchaseDetailsList) {
      debugPrint('FiddleTalkInAppPurchase: Purchase status: ${fiddletalkPurchaseDetails.status} for product: ${fiddletalkPurchaseDetails.productID}');
      
      try {
        switch (fiddletalkPurchaseDetails.status) {
          case PurchaseStatus.pending:
            // 购买等待中，保持loading状态
            debugPrint('FiddleTalkInAppPurchase: Purchase pending for ${fiddletalkPurchaseDetails.productID}');
            break;
            
          case PurchaseStatus.purchased:
            // 购买成功
            await _fiddletalkHandlePurchaseSuccess(fiddletalkPurchaseDetails);
            break;
            
          case PurchaseStatus.error:
            // 购买失败
            _fiddletalkHandlePurchaseError(fiddletalkPurchaseDetails);
            break;
            
          case PurchaseStatus.canceled:
            // 购买取消
            _fiddletalkHandlePurchaseCanceled();
            break;
            
          case PurchaseStatus.restored:
            // 恢复购买（对于消耗品通常不适用）
            await _fiddletalkHandlePurchaseSuccess(fiddletalkPurchaseDetails);
            break;
        }
      } catch (e) {
        debugPrint('FiddleTalkInAppPurchase: Error handling purchase update: $e');
        _fiddletalkIsProcessing = false;
        _fiddletalkOnLoadingStateChanged?.call(false);
        _fiddletalkOnPurchaseResult?.call('Error processing purchase: $e', false);
      }

      // 完成购买（重要：必须调用以完成交易）
      if (fiddletalkPurchaseDetails.pendingCompletePurchase) {
        try {
          await _fiddletalkInAppPurchase.completePurchase(fiddletalkPurchaseDetails);
          debugPrint('FiddleTalkInAppPurchase: Purchase completed for ${fiddletalkPurchaseDetails.productID}');
        } catch (e) {
          debugPrint('FiddleTalkInAppPurchase: Failed to complete purchase: $e');
        }
      }
    }
  }

  /// 处理FiddleTalk购买成功
  Future<void> _fiddletalkHandlePurchaseSuccess(PurchaseDetails fiddletalkPurchaseDetails) async {
    try {
      // 根据商品ID查找对应的金币数量
      final fiddletalkAllItems = FiddleTalkStoreItemModel.getAllItems();
      FiddleTalkStoreItemModel? fiddletalkItem;
      for (final fiddletalkStoreItem in fiddletalkAllItems) {
        if (fiddletalkStoreItem.code == fiddletalkPurchaseDetails.productID) {
          fiddletalkItem = fiddletalkStoreItem;
          break;
        }
      }

      if (fiddletalkItem != null) {
        // 添加金币到用户账户
        await _fiddletalkCoinService.addCoins(fiddletalkItem.exchangeCoin);
        _fiddletalkIsProcessing = false;
        _fiddletalkOnLoadingStateChanged?.call(false);
        _fiddletalkOnPurchaseResult?.call(
          'Purchase successful! You received ${fiddletalkItem.exchangeCoin} coins.',
          true,
        );
        debugPrint('FiddleTalkInAppPurchase: Added ${fiddletalkItem.exchangeCoin} coins for product ${fiddletalkPurchaseDetails.productID}');
      } else {
        _fiddletalkIsProcessing = false;
        _fiddletalkOnLoadingStateChanged?.call(false);
        _fiddletalkOnPurchaseResult?.call('Purchase successful but product not found in store', false);
        debugPrint('FiddleTalkInAppPurchase: Product not found in store items: ${fiddletalkPurchaseDetails.productID}');
      }
    } catch (e) {
      _fiddletalkIsProcessing = false;
      _fiddletalkOnLoadingStateChanged?.call(false);
      _fiddletalkOnPurchaseResult?.call('Purchase successful but failed to add coins: $e', false);
      debugPrint('FiddleTalkInAppPurchase: Failed to handle purchase success: $e');
    }
  }

  /// 处理FiddleTalk购买失败
  void _fiddletalkHandlePurchaseError(PurchaseDetails fiddletalkPurchaseDetails) {
    _fiddletalkIsProcessing = false;
    _fiddletalkOnLoadingStateChanged?.call(false);
    final String fiddletalkErrorMessage = fiddletalkPurchaseDetails.error?.message ?? 'Unknown error';
    _fiddletalkOnPurchaseResult?.call('Purchase failed: $fiddletalkErrorMessage', false);
    debugPrint('FiddleTalkInAppPurchase: Purchase error: ${fiddletalkPurchaseDetails.error}');
  }

  /// 处理FiddleTalk购买取消
  void _fiddletalkHandlePurchaseCanceled() {
    _fiddletalkIsProcessing = false;
    _fiddletalkOnLoadingStateChanged?.call(false);
    _fiddletalkOnPurchaseResult?.call('Purchase was canceled by user', false);
    debugPrint('FiddleTalkInAppPurchase: Purchase was canceled by user');
  }

  /// 设置FiddleTalk购买结果回调
  void setFiddleTalkOnPurchaseResult(Function(String message, bool isSuccess) fiddletalkCallback) {
    _fiddletalkOnPurchaseResult = fiddletalkCallback;
  }

  /// 设置FiddleTalk加载状态回调
  void setFiddleTalkOnLoadingStateChanged(Function(bool isLoading) fiddletalkCallback) {
    _fiddletalkOnLoadingStateChanged = fiddletalkCallback;
  }

  /// 获取FiddleTalk商品详情
  ProductDetails? getFiddleTalkProductDetails(String fiddletalkProductId) {
    return _fiddletalkProducts[fiddletalkProductId];
  }

  /// FiddleTalk商店是否可用
  bool get fiddletalkStoreAvailable => _fiddletalkStoreAvailable;

  /// 获取已查询的FiddleTalk商品
  Map<String, ProductDetails> get fiddletalkProducts => _fiddletalkProducts;

  /// 是否正在处理购买
  bool get fiddletalkIsProcessing => _fiddletalkIsProcessing;

  /// 销毁FiddleTalk服务
  void disposeFiddleTalk() {
    _fiddletalkPurchaseSubscription.cancel();
    _fiddletalkProducts.clear();
    _fiddletalkOnPurchaseResult = null;
    _fiddletalkOnLoadingStateChanged = null;
    _fiddletalkIsProcessing = false;
    debugPrint('FiddleTalkInAppPurchase: Service disposed');
  }
} 