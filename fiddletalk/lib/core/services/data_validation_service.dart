import 'dart:io';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/comment_model.dart';
import 'image_storage_service.dart';

/// Service for validating and cleaning up data integrity
class DataValidationService {
  static final DataValidationService _instance = DataValidationService._internal();
  factory DataValidationService() => _instance;
  DataValidationService._internal();

  final ImageStorageService _imageStorageService = ImageStorageService();

  /// Validate and clean up all data on app startup
  Future<void> validateAndCleanupData() async {
    try {
      debugPrint('Starting data validation and cleanup...');

      await _validatePostImages();
      await _validateUserData();
      await _resetCommentData(); // 重置评论数据以应用新的预设点赞状态

      debugPrint('Data validation and cleanup completed.');
    } catch (e) {
      debugPrint('Error during data validation: $e');
    }
  }

  /// Validate and fix image paths in all posts
  Future<void> _validatePostImages() async {
    try {
      final postsBox = await Hive.openBox<PostModel>('posts');
      final posts = postsBox.values.toList();
      bool hasChanges = false;

      for (final post in posts) {
        if (post.images.isNotEmpty) {
          final validImages = await _imageStorageService.validateAndFixImagePaths(post.images);

          if (validImages.length != post.images.length) {
            // Update post with valid images only
            final updatedPost = post.copyWith(images: validImages);
            await postsBox.put(post.id, updatedPost);
            hasChanges = true;

            debugPrint('Updated post ${post.id}: removed ${post.images.length - validImages.length} invalid images');
          }
        }
      }

      // 强制清理特定的无效图片路径
      await _forceCleanInvalidPaths(postsBox);

      if (hasChanges) {
        debugPrint('Post image validation completed with changes');
      } else {
        debugPrint('Post image validation completed - no changes needed');
      }
    } catch (e) {
      debugPrint('Error validating post images: $e');
    }
  }

  /// 强制清理已知的无效图片路径
  Future<void> _forceCleanInvalidPaths(Box<PostModel> postsBox) async {
    try {
      final posts = postsBox.values.toList();
      bool hasChanges = false;

      for (final post in posts) {
        if (post.images.isNotEmpty) {
          final cleanedImages = post.images.where((imagePath) {
            // 移除包含特定时间戳的无效路径
            if (imagePath.contains('post_1753072163018_3018.jpg')) {
              debugPrint('Removing invalid image path: $imagePath');
              return false;
            }
            return true;
          }).toList();

          if (cleanedImages.length != post.images.length) {
            final updatedPost = post.copyWith(images: cleanedImages);
            await postsBox.put(post.id, updatedPost);
            hasChanges = true;
            debugPrint('Force cleaned post ${post.id}: removed invalid image paths');
          }
        }
      }

      if (hasChanges) {
        debugPrint('Force cleanup completed with changes');
      }
    } catch (e) {
      debugPrint('Error during force cleanup: $e');
    }
  }

  /// Validate user data integrity
  Future<void> _validateUserData() async {
    try {
      // Add user data validation logic here if needed
      debugPrint('User data validation completed');
    } catch (e) {
      debugPrint('Error validating user data: $e');
    }
  }

  /// Reset comment data to apply consistent like states
  Future<void> _resetCommentData() async {
    try {
      // 清理现有评论数据，让MockDataService重新生成一致的评论状态
      // 确保所有模拟评论初始状态都是未点赞，只有用户操作后才变为已点赞
      final commentsBox = await Hive.openBox<CommentModel>('comments');

      // 强制清理所有模拟评论数据，确保真实的点赞系统
      final commentsToRemove = <String>[];
      for (final comment in commentsBox.values) {
        // 如果评论ID符合模拟数据的格式，则标记为删除
        // 主评论格式：comment_post_X_Y (4个部分)
        // 回复评论格式：comment_post_X_Y_reply_Z (6个部分)
        if (comment.id.startsWith('comment_post_') && comment.id.contains('_')) {
          final parts = comment.id.split('_');
          if (parts.length == 4 || (parts.length == 6 && parts[4] == 'reply')) {
            commentsToRemove.add(comment.id);
          }
        }
      }

      // 删除模拟评论数据
      for (final commentId in commentsToRemove) {
        await commentsBox.delete(commentId);
      }

      // 额外清理：删除所有可能的旧数据
      await _forceCleanAllMockComments(commentsBox);

      if (commentsToRemove.isNotEmpty) {
        debugPrint('Reset ${commentsToRemove.length} mock comments to ensure consistent initial state (all unliked)');
      } else {
        debugPrint('No mock comments to reset');
      }
    } catch (e) {
      debugPrint('Error resetting comment data: $e');
    }
  }

  /// 强制清理所有模拟评论数据
  Future<void> _forceCleanAllMockComments(Box<CommentModel> commentsBox) async {
    try {
      // 清理所有以 comment_post_ 开头的评论
      final allKeys = commentsBox.keys.toList();
      final mockCommentKeys = allKeys.where((key) =>
        key.toString().startsWith('comment_post_')).toList();

      for (final key in mockCommentKeys) {
        await commentsBox.delete(key);
      }

      if (mockCommentKeys.isNotEmpty) {
        debugPrint('Force cleaned ${mockCommentKeys.length} additional mock comments');
      }
    } catch (e) {
      debugPrint('Error during force clean: $e');
    }
  }

  /// Check if a file path is valid
  Future<bool> isValidFilePath(String path) async {
    if (path.startsWith('assets/')) {
      return true; // Asset paths are always valid
    }
    
    if (path.startsWith('http')) {
      return true; // Network paths are assumed valid
    }
    
    if (path.startsWith('/')) {
      try {
        final file = File(path);
        return await file.exists();
      } catch (e) {
        return false;
      }
    }
    
    return false;
  }

  /// Get storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final totalSize = await _imageStorageService.getTotalStorageSize();
      final allImages = await _imageStorageService.getAllStoredImages();
      
      return {
        'totalSize': totalSize,
        'totalFiles': allImages.length,
        'formattedSize': _imageStorageService.formatBytes(totalSize),
      };
    } catch (e) {
      debugPrint('Error getting storage stats: $e');
      return {
        'totalSize': 0,
        'totalFiles': 0,
        'formattedSize': '0 B',
      };
    }
  }

  /// Clean up orphaned image files
  Future<void> cleanupOrphanedImages() async {
    try {
      debugPrint('Starting orphaned image cleanup...');
      
      // Get all stored images
      final allImages = await _imageStorageService.getAllStoredImages();
      
      // Get all image paths referenced in posts
      final postsBox = await Hive.openBox<PostModel>('posts');
      final referencedPaths = <String>{};
      
      for (final post in postsBox.values) {
        referencedPaths.addAll(post.images);
      }
      
      // Find orphaned files
      int deletedCount = 0;
      for (final imageFile in allImages) {
        final isReferenced = referencedPaths.contains(imageFile.path);
        
        if (!isReferenced) {
          try {
            await imageFile.delete();
            deletedCount++;
            debugPrint('Deleted orphaned image: ${imageFile.path}');
          } catch (e) {
            debugPrint('Failed to delete orphaned image: ${imageFile.path}, Error: $e');
          }
        }
      }
      
      debugPrint('Orphaned image cleanup completed. Deleted $deletedCount files.');
    } catch (e) {
      debugPrint('Error during orphaned image cleanup: $e');
    }
  }

  /// Repair data integrity issues
  Future<void> repairDataIntegrity() async {
    try {
      debugPrint('Starting data integrity repair...');
      
      await _validatePostImages();
      await cleanupOrphanedImages();
      await _imageStorageService.cleanupUnusedImages();
      
      debugPrint('Data integrity repair completed.');
    } catch (e) {
      debugPrint('Error during data integrity repair: $e');
    }
  }
}
