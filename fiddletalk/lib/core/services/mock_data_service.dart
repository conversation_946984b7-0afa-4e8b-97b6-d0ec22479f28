import 'dart:math';
import 'package:hive/hive.dart';
import '../../shared/models/post_model.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/comment_model.dart';
import '../constants/app_constants.dart';
import '../../shared/providers/user_data_provider.dart';

/// Mock data service for development and testing
class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal() {
    _initializeHive();
  }
  
  Future<void> _initializeHive() async {
    try {
      _userPostsBox = await Hive.openBox<PostModel>('user_posts');
      _loadUserPostsFromHive();
    } catch (e) {
      print('Error initializing Hive for posts: $e');
    }
  }
  
  void _loadUserPostsFromHive() {
    if (_userPostsBox != null) {
      _userCreatedPosts.clear();
      _userCreatedPosts.addAll(_userPostsBox!.values);
      // Sort by creation date (newest first)
      _userCreatedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    }
  }

  // 图片使用计数器，确保不超过3次重复使用
  final Map<String, int> _imageUsageCount = {};
  final Random _random = Random();

  // 缓存帖子图片映射，确保每个帖子的图片固定
  final Map<String, String?> _postImageCache = {};
  
  // 存储用户创建的新帖子
  final List<PostModel> _userCreatedPosts = [];
  Box<PostModel>? _userPostsBox;

  // 根据帖子内容类型分配本地图片（固定映射）
  String? _getImageForPost(String postId, String title, String content, String category) {
    // 如果已经缓存了该帖子的图片，直接返回
    if (_postImageCache.containsKey(postId)) {
      return _postImageCache[postId];
    }

    List<String> availableImages = [];

    // 根据内容判断使用哪类图片
    if (title.contains('曲谱') || title.contains('练习') || content.contains('曲谱') ||
        title.contains('Bach') || title.contains('Partita') || title.contains('练习了图片里面的曲谱')) {
      // 使用曲谱图片
      availableImages = List.generate(10, (i) => 'assets/Violin score/${i + 1}.png');
    } else if (category == '设备评测' || title.contains('Violin') || title.contains('String') ||
               title.contains('设备') || title.contains('选购') || title.contains('保养')) {
      // 使用乐器图片
      availableImages = List.generate(10, (i) => 'assets/violin/${i + 1}.png');
    } else if (category == '技巧教程' || title.contains('Technique') || title.contains('技巧') ||
               title.contains('练习') || title.contains('演奏')) {
      // 使用小提琴手图片
      availableImages = List.generate(10, (i) => 'assets/violinist/${i + 1}.png');
    } else {
      // 默认使用小提琴手图片
      availableImages = List.generate(10, (i) => 'assets/violinist/${i + 1}.png');
    }

    // 使用帖子ID的哈希值来确定图片，确保每个帖子的图片固定
    final hash = postId.hashCode.abs();
    final selectedImage = availableImages[hash % availableImages.length];

    // 缓存图片映射
    _postImageCache[postId] = selectedImage;

    return selectedImage;
  }

  // Mock users data with unique avatars
  final List<UserModel> _mockUsers = [
    UserModel(
      id: '1',
      username: 'violin_master',
      nickname: 'Violin Master',
      avatar: 'assets/images/1.png',
      bio: 'Professional violinist with 15 years of experience',
      email: '<EMAIL>',
      level: 4,
      coins: 2500,
      followersCount: 1250,
      followingCount: 180,
      postsCount: 89,
      badges: ['Technique Sharing Expert', 'Master Player'],
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now(),
      learningYears: '15 years',
      specialtyStyle: 'Classical & Contemporary',
      personalGoal: 'Inspire the next generation of violinists',
    ),
    UserModel(
      id: '2',
      username: 'classical_lover',
      nickname: 'Classical Enthusiast',
      avatar: 'assets/images/2.png',
      bio: 'Bach and Vivaldi are my inspiration',
      email: '<EMAIL>',
      level: 2,
      coins: 850,
      followersCount: 420,
      followingCount: 95,
      postsCount: 34,
      badges: ['Active User'],
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now(),
      learningYears: '5 years',
      specialtyStyle: 'Classical Music',
      personalGoal: 'Master Bach\'s Partitas',
    ),
    UserModel(
      id: '3',
      username: 'beginner_player',
      nickname: 'Learning Journey',
      avatar: 'assets/images/3.png',
      bio: 'Just started my violin journey, excited to learn!',
      email: '<EMAIL>',
      level: 0,
      coins: 120,
      followersCount: 45,
      followingCount: 150,
      postsCount: 12,
      badges: ['Helpful Member'],
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      learningYears: '6 months',
      specialtyStyle: 'Learning Basics',
      personalGoal: 'Play my first complete piece',
    ),
    UserModel(
      id: '4',
      username: 'technique_expert',
      nickname: 'Technique Expert',
      avatar: 'assets/images/4.png',
      bio: 'Sharing advanced violin techniques and tips',
      email: '<EMAIL>',
      level: 3,
      coins: 1800,
      followersCount: 890,
      followingCount: 120,
      postsCount: 67,
      badges: ['Technique Master', 'Community Helper'],
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      updatedAt: DateTime.now(),
      learningYears: '12 years',
      specialtyStyle: 'Advanced Techniques',
      personalGoal: 'Help others improve their skills',
    ),
    UserModel(
      id: '5',
      username: 'music_student',
      nickname: 'Music Student',
      avatar: 'assets/images/5.png',
      bio: 'Studying music at conservatory',
      email: '<EMAIL>',
      level: 2,
      coins: 650,
      followersCount: 320,
      followingCount: 200,
      postsCount: 28,
      badges: ['Student', 'Practice Enthusiast'],
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now(),
      learningYears: '8 years',
      specialtyStyle: 'Classical & Modern',
      personalGoal: 'Graduate with honors',
    ),
    UserModel(
      id: '6',
      username: 'orchestra_player',
      nickname: 'Orchestra Player',
      avatar: 'assets/images/6.png',
      bio: 'First violin in city orchestra',
      email: '<EMAIL>',
      level: 4,
      coins: 2200,
      followersCount: 1100,
      followingCount: 80,
      postsCount: 45,
      badges: ['Professional', 'Orchestra Member'],
      createdAt: DateTime.now().subtract(const Duration(days: 300)),
      updatedAt: DateTime.now(),
      learningYears: '18 years',
      specialtyStyle: 'Orchestral Music',
      personalGoal: 'Inspire through performance',
    ),
    UserModel(
      id: '7',
      username: 'chamber_musician',
      nickname: 'Chamber Musician',
      avatar: 'assets/images/7.png',
      bio: 'Passionate about chamber music',
      email: '<EMAIL>',
      level: 3,
      coins: 1400,
      followersCount: 680,
      followingCount: 150,
      postsCount: 52,
      badges: ['Chamber Expert', 'Ensemble Player'],
      createdAt: DateTime.now().subtract(const Duration(days: 250)),
      updatedAt: DateTime.now(),
      learningYears: '10 years',
      specialtyStyle: 'Chamber Music',
      personalGoal: 'Perfect ensemble playing',
    ),
    UserModel(
      id: '8',
      username: 'young_prodigy',
      nickname: 'Young Prodigy',
      avatar: 'assets/images/8.png',
      bio: 'Young talent with big dreams',
      email: '<EMAIL>',
      level: 2,
      coins: 950,
      followersCount: 580,
      followingCount: 90,
      postsCount: 38,
      badges: ['Rising Star', 'Competition Winner'],
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now(),
      learningYears: '6 years',
      specialtyStyle: 'Competition Pieces',
      personalGoal: 'Win international competition',
    ),
    UserModel(
      id: '9',
      username: 'folk_fiddler',
      nickname: 'Folk Fiddler',
      avatar: 'assets/images/9.png',
      bio: 'Traditional folk music enthusiast',
      email: '<EMAIL>',
      level: 3,
      coins: 1200,
      followersCount: 750,
      followingCount: 110,
      postsCount: 43,
      badges: ['Folk Expert', 'Cultural Preservationist'],
      createdAt: DateTime.now().subtract(const Duration(days: 220)),
      updatedAt: DateTime.now(),
      learningYears: '14 years',
      specialtyStyle: 'Folk & Traditional',
      personalGoal: 'Preserve traditional music',
    ),
    UserModel(
      id: '10',
      username: 'modern_violinist',
      nickname: 'Modern Violinist',
      avatar: 'assets/images/10.png',
      bio: 'Exploring contemporary violin music',
      email: '<EMAIL>',
      level: 3,
      coins: 1600,
      followersCount: 920,
      followingCount: 140,
      postsCount: 56,
      badges: ['Contemporary Expert', 'Innovation Leader'],
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now(),
      learningYears: '11 years',
      specialtyStyle: 'Contemporary & Experimental',
      personalGoal: 'Push violin boundaries',
    ),
  ];

  // Mock comment users data (using actor avatars)
  final List<UserModel> _commentUsers = [
    UserModel(
      id: 'comment_user_1',
      username: 'music_lover',
      nickname: 'Music Lover',
      avatar: 'assets/actor/boy.png',
      bio: 'Love listening and learning',
      email: '<EMAIL>',
      level: 1,
      coins: 300,
      followersCount: 120,
      followingCount: 80,
      postsCount: 5,
      badges: ['Music Enthusiast'],
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
      learningYears: '2 years',
      specialtyStyle: 'Various',
      personalGoal: 'Appreciate music better',
    ),
    UserModel(
      id: 'comment_user_2',
      username: 'practice_buddy',
      nickname: 'Practice Buddy',
      avatar: 'assets/actor/girl.png',
      bio: 'Always practicing and improving',
      email: '<EMAIL>',
      level: 1,
      coins: 450,
      followersCount: 200,
      followingCount: 150,
      postsCount: 8,
      badges: ['Dedicated Learner'],
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now(),
      learningYears: '3 years',
      specialtyStyle: 'Practice Focused',
      personalGoal: 'Consistent improvement',
    ),
  ];

  // Mock posts data
  List<PostModel> _generateMockPosts() {
    final List<PostModel> posts = [];
    final List<String> sampleTitles = [
      'Essential Vibrato Techniques for Beginners',
      'Mastering the Spiccato Bow Technique',
      'Practiced the Score in the Image: Performance Insights',
      'Choosing Your First Violin: A Complete Guide',
      'Advanced Position Shifting Exercises',
      'The Art of Musical Phrasing in Violin',
      'String Maintenance and Care Tips',
      'Overcoming Stage Fright: A Violinist\'s Guide',
      'Double Stops: From Basic to Advanced',
      'Exploring Contemporary Violin Techniques',
      'The Perfect Practice Routine',
      'Understanding Violin Acoustics',
      'Bow Hold Fundamentals',
      'Intonation Training Methods',
      'Classical vs Electric Violin',
    ];

    final List<String> sampleContents = [
      'Today I want to share some essential vibrato techniques that have helped me develop a more expressive sound. Vibrato is one of the most important techniques for adding emotion and warmth to your playing...',
      'Spiccato is a challenging but rewarding bow technique. Here\'s my step-by-step approach to mastering this bouncing bow stroke that adds brilliance to your performance...',
      'Recently practiced the score shown in the image and wanted to share some performance insights. This piece is a classic that every serious violinist should study...',
      'Choosing your first violin can be overwhelming. Here\'s everything you need to know about selecting an instrument that will serve you well on your musical journey...',
      'Position shifting is crucial for advanced repertoire. These exercises have helped me develop smooth and accurate shifts between positions...',
      'Musical phrasing is what separates good playing from great playing. Let\'s explore how to bring your musical lines to life...',
      'Proper string maintenance can extend the life of your instrument and improve your sound quality. Here are my top tips for string care...',
      'Performance anxiety is common among musicians. Here are strategies that have helped me and my students overcome stage fright...',
      'Double stops add richness and complexity to violin music. Let\'s work through some exercises to build this important skill...',
      'Modern violin techniques open up new expressive possibilities. Here\'s an exploration of extended techniques for the contemporary violinist...',
      'A well-structured practice routine is essential for progress. Here\'s the routine that has transformed my playing...',
      'Understanding how your violin produces sound can help you play more effectively. Let\'s dive into the physics of violin acoustics...',
      'The bow hold is the foundation of good technique. Let\'s review the fundamentals and common mistakes to avoid...',
      'Good intonation is essential for beautiful violin playing. These training methods will help you develop a reliable sense of pitch...',
      'Should you choose a classical or electric violin? Let\'s compare the pros and cons of each option...',
    ];

    for (int i = 0; i < sampleTitles.length; i++) {
      final user = _mockUsers[i % _mockUsers.length];
      final category = AppConstants.contentCategories[i % AppConstants.contentCategories.length];
      final title = sampleTitles[i];
      final content = sampleContents[i];

      // 为每个帖子分配一张本地图片（使用帖子ID确保固定）
      final postId = 'post_$i';
      final image = _getImageForPost(postId, title, content, category);

      // 计算实际的评论总数（包括回复）
      int actualCommentsCount = _calculateActualCommentsCount(postId);

      posts.add(PostModel(
        id: postId,
        title: title,
        content: content,
        images: image != null ? [image] : [],
        category: category,
        musicStyle: i % 2 == 0 ? AppConstants.musicStyles[i % AppConstants.musicStyles.length] : null,
        playingLevel: AppConstants.playingLevels[i % AppConstants.playingLevels.length],
        tags: _generateRandomTags(i),
        authorId: user.id,
        author: user,
        likesCount: (i + 1) * 12 + (i * 3),
        commentsCount: actualCommentsCount, // 使用实际计算的评论数（包括回复）
        collectionsCount: (i + 1) * 8,
        isLiked: i % 4 == 0,
        isCollected: i % 5 == 0,
        isPinned: i < 2,
        isHighlighted: i % 7 == 0,
        createdAt: DateTime.now().subtract(Duration(hours: i * 2 + 1)),
        updatedAt: DateTime.now().subtract(Duration(hours: i * 2)),
        viewsCount: (i + 1) * 45 + (i * 8),
      ));
    }

    return posts;
  }

  /// 计算实际的评论总数（包括回复）
  int _calculateActualCommentsCount(String postId) {
    // 直接生成评论并计算总数，确保与实际生成的评论数量完全一致
    final comments = _generateCommentsForPost(postId);

    int totalCount = comments.length; // 主评论数量

    // 计算所有回复数量
    for (final comment in comments) {
      totalCount += comment.replies.length;
    }

    return totalCount;
  }

  /// Get comments for a specific post
  Future<List<CommentModel>> getCommentsForPost(String postId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    return _generateCommentsForPost(postId);
  }

  List<CommentModel> _generateCommentsForPost(String postId) {
    final List<String> sampleComments = [
      'Great technique! This really helped me improve my vibrato.',
      'Thank you for sharing this. I\'ve been struggling with this piece.',
      'Your bow hold looks perfect. Any tips for beginners?',
      'Beautiful sound quality! What violin are you using?',
      'This is exactly what I needed to work on. Thanks!',
      'Amazing performance! How long have you been playing?',
      'Could you share more about your practice routine?',
      'The intonation is spot on. Very impressive!',
      'I love how you explained the fingering technique.',
      'This piece is so challenging. You make it look easy!',
      'Your musical expression is wonderful.',
      'Thanks for the detailed explanation. Very helpful!',
      'I\'m inspired to practice more after watching this.',
      'The dynamics in your playing are excellent.',
      'Could you do a tutorial on this technique?',
    ];

    final List<String> authorReplies = [
      'Thank you so much for the kind words!',
      'I\'m glad this was helpful to you.',
      'Practice makes perfect! Keep it up!',
      'Feel free to ask if you have any questions.',
      'I appreciate your feedback!',
    ];

    final comments = <CommentModel>[];

    // 使用postId作为种子确保每次生成相同的评论数量和内容
    final postRandom = Random(postId.hashCode);
    final commentCount = postRandom.nextInt(8) + 5; // 5-12 comments

    // Get the post author for potential replies
    final postAuthor = _getPostAuthor(postId);

    for (int i = 0; i < commentCount; i++) {
      UserModel user;
      String content;
      bool isAuthorComment = false;

      // 20% chance for author to comment
      if (postRandom.nextDouble() < 0.2 && postAuthor != null) {
        user = postAuthor;
        content = authorReplies[postRandom.nextInt(authorReplies.length)];
        isAuthorComment = true;
      } else {
        // Mix of post authors and comment users
        final allUsers = [..._mockUsers, ..._commentUsers];
        user = allUsers[postRandom.nextInt(allUsers.length)];
        content = sampleComments[postRandom.nextInt(sampleComments.length)];
      }

      // 生成固定的评论数据，确保一致性
      final commentId = 'comment_${postId}_$i';
      final commentHash = commentId.hashCode.abs();

      // 真实的点赞系统：
      // 1. 所有评论初始状态都是未点赞 (isLiked = false)
      // 2. 点赞数由其他用户产生，不包含当前用户
      // 3. 只有用户主动点击才会变为已点赞状态
      final comment = CommentModel(
        id: commentId,
        postId: postId,
        authorId: user.id,
        authorName: user.nickname,
        authorAvatar: user.avatar,
        content: content,
        createdAt: DateTime.now().subtract(Duration(
          hours: postRandom.nextInt(72) + 1,
          minutes: postRandom.nextInt(60),
        )),
        updatedAt: DateTime.now().subtract(Duration(
          hours: postRandom.nextInt(72) + 1,
          minutes: postRandom.nextInt(60),
        )),
        likesCount: commentHash % 15 + 1, // 1-15个其他用户的点赞
        isLiked: false, // 当前用户初始状态：未点赞
        replies: _generateRepliesForComment(commentId, postAuthor, postRandom),
      );
      comments.add(comment);
    }

    // Sort by creation time (newest first)
    comments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return comments;
  }

  /// Get post author by post ID
  UserModel? _getPostAuthor(String postId) {
    try {
      final postIndex = int.parse(postId.replaceAll('post_', ''));
      if (postIndex < _mockUsers.length) {
        return _mockUsers[postIndex];
      }
    } catch (e) {
      // Handle parsing error
    }
    return null;
  }

  List<CommentModel> _generateRepliesForComment(String commentId, UserModel? postAuthor, Random random) {
    // 30% chance of having replies
    if (random.nextDouble() > 0.3) return [];

    final List<String> sampleReplies = [
      'I agree with you!',
      'Thanks for the tip!',
      'That\'s a great point.',
      'I had the same experience.',
      'You\'re absolutely right.',
      'Thanks for sharing!',
    ];

    final List<String> authorReplies = [
      'Thank you for your comment!',
      'I\'m glad you found it helpful!',
      'Great question! Let me know if you need more help.',
      'Thanks for the feedback!',
    ];

    final replyCount = random.nextInt(3) + 1; // 1-3 replies
    final replies = <CommentModel>[];

    for (int i = 0; i < replyCount; i++) {
      UserModel user;
      String content;

      // 30% chance for post author to reply
      if (random.nextDouble() < 0.3 && postAuthor != null) {
        user = postAuthor;
        content = authorReplies[random.nextInt(authorReplies.length)];
      } else {
        // Mix of all users
        final allUsers = [..._mockUsers, ..._commentUsers];
        user = allUsers[random.nextInt(allUsers.length)];
        content = sampleReplies[random.nextInt(sampleReplies.length)];
      }

      // 生成固定的回复数据
      final replyId = '${commentId}_reply_$i';
      final replyHash = replyId.hashCode.abs();

      final reply = CommentModel(
        id: replyId,
        postId: commentId.split('_')[1], // Extract post ID
        authorId: user.id,
        authorName: user.nickname,
        authorAvatar: user.avatar,
        content: content,
        createdAt: DateTime.now().subtract(Duration(
          hours: random.nextInt(48) + 1,
          minutes: random.nextInt(60),
        )),
        updatedAt: DateTime.now().subtract(Duration(
          hours: random.nextInt(48) + 1,
          minutes: random.nextInt(60),
        )),
        likesCount: replyHash % 5 + 1, // 1-5个其他用户的点赞
        isLiked: false, // 当前用户初始状态：未点赞
      );
      replies.add(reply);
    }

    return replies;
  }

  List<String> _generateRandomTags(int index) {
    final availableTags = AppConstants.topicTags;
    final tagCount = (index % 3) + 1; // 1-3 tags per post
    final selectedTags = <String>[];
    
    for (int i = 0; i < tagCount; i++) {
      final tag = availableTags[(index + i) % availableTags.length];
      if (!selectedTags.contains(tag)) {
        selectedTags.add(tag);
      }
    }
    
    return selectedTags;
  }

  // Public methods
  Future<List<PostModel>> getPosts({
    int page = 1,
    int limit = 10,
    String? category,
    String? musicStyle,
    String? playingLevel,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    List<PostModel> allPosts = [
      // Include user created posts first
      ..._userCreatedPosts,
      // Then include mock posts
      ..._generateMockPosts(),
    ];
    
    // Apply filters
    if (category != null && category != 'All') {
      allPosts = allPosts.where((post) => post.category == category).toList();
    }
    
    if (musicStyle != null && musicStyle != 'All') {
      allPosts = allPosts.where((post) => post.musicStyle == musicStyle).toList();
    }
    
    if (playingLevel != null && playingLevel != 'All') {
      allPosts = allPosts.where((post) => post.playingLevel == playingLevel).toList();
    }
    
    // Sort by creation date (newest first)
    allPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;
    
    if (startIndex >= allPosts.length) {
      return [];
    }
    
    return allPosts.sublist(
      startIndex,
      endIndex > allPosts.length ? allPosts.length : endIndex,
    );
  }

  Future<List<UserModel>> getUsers() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_mockUsers);
  }

  Future<PostModel?> getPostById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final posts = _generateMockPosts();
    try {
      return posts.firstWhere((post) => post.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<UserModel?> getUserById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    try {
      return _mockUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // Search functionality
  Future<List<PostModel>> searchPosts(String query) async {
    await Future.delayed(const Duration(milliseconds: 600));
    final posts = _generateMockPosts();
    
    return posts.where((post) {
      return post.title.toLowerCase().contains(query.toLowerCase()) ||
             post.content.toLowerCase().contains(query.toLowerCase()) ||
             post.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  // Create new post
  Future<PostModel> createPost(PostModel post) async {
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Get current user data
    final userDataProvider = UserDataProvider();
    final userData = userDataProvider.userData;
    
    // Create user model from current user data
    final currentUser = UserModel(
      id: userData['id'] as String,
      username: userData['username'] as String,
      nickname: userData['nickname'] as String,
      avatar: userData['avatar'] as String?,
      bio: userData['bio'] as String?,
      email: userData['email'] as String,
      level: userData['level'] as int,
      coins: userData['coins'] as int,
      followersCount: userData['followersCount'] as int,
      followingCount: userData['followingCount'] as int,
      postsCount: userData['postsCount'] as int,
      badges: List<String>.from(userData['badges'] as List),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isVerified: userData['isVerified'] as bool,
      learningYears: userData['learningYears'] as String?,
      specialtyStyle: userData['specialtyStyle'] as String?,
      personalGoal: userData['personalGoal'] as String?,
      isOnline: userData['isOnline'] as bool,
    );
    
    // Add the current user as author
    post = post.copyWith(
      author: currentUser,
      authorId: currentUser.id,
    );

    // Store the new post in memory
    _userCreatedPosts.insert(0, post); // Insert at beginning for latest first
    
    // Store the new post in Hive for persistence
    if (_userPostsBox != null) {
      await _userPostsBox!.put(post.id, post);
    }

    // Simulate successful creation
    return post;
  }
  
  /// 获取用户创建的帖子数量
  int getUserCreatedPostsCount() {
    return _userCreatedPosts.length;
  }
  
  /// 获取用户创建的所有帖子
  List<PostModel> getUserCreatedPosts() {
    return List.from(_userCreatedPosts);
  }
}
