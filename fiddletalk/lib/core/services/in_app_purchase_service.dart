import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

/// FiddleTalk内购服务 - 管理金币和免费发帖额度
class InAppPurchaseService extends ChangeNotifier {
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal() {
    _initializeFiddleTalkService();
  }

  // Hive box for storing FiddleTalk purchase data
  Box<dynamic>? _fiddletalkPurchaseDataBox;

  // FiddleTalk current state
  int _fiddletalkCurrentCoins = 0;
  int _fiddletalkFreePostQuota = 3; // 默认3次免费发帖额度（不是金币）
  bool _fiddletalkIsInitialized = false;

  // Keys for Hive storage
  static const String _fiddletalkCoinsKey = 'fiddletalk_coins';
  static const String _fiddletalkFreePostQuotaKey = 'fiddletalk_free_post_quota';

  /// Initialize the FiddleTalk service
  Future<void> _initializeFiddleTalkService() async {
    try {
      _fiddletalkPurchaseDataBox = await Hive.openBox('fiddletalk_purchase_data');
      _loadFiddleTalkDataFromLocal();
      _fiddletalkIsInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing FiddleTalk InAppPurchaseService: $e');
    }
  }

  /// Load FiddleTalk data from local storage
  void _loadFiddleTalkDataFromLocal() {
    if (_fiddletalkPurchaseDataBox != null) {
      _fiddletalkCurrentCoins = _fiddletalkPurchaseDataBox!.get(_fiddletalkCoinsKey, defaultValue: 0) as int;
      _fiddletalkFreePostQuota = _fiddletalkPurchaseDataBox!.get(_fiddletalkFreePostQuotaKey, defaultValue: 3) as int;
    }
  }

  /// Save FiddleTalk data to local storage
  Future<void> _saveFiddleTalkDataToLocal() async {
    if (_fiddletalkPurchaseDataBox != null) {
      await _fiddletalkPurchaseDataBox!.put(_fiddletalkCoinsKey, _fiddletalkCurrentCoins);
      await _fiddletalkPurchaseDataBox!.put(_fiddletalkFreePostQuotaKey, _fiddletalkFreePostQuota);
    }
  }

  // FiddleTalk Getters
  int get currentCoins => _fiddletalkCurrentCoins;
  int get freePostQuota => _fiddletalkFreePostQuota; // 免费发帖次数
  bool get isInitialized => _fiddletalkIsInitialized;
  bool get canCreatePost => _fiddletalkFreePostQuota > 0 || _fiddletalkCurrentCoins >= FiddleTalkInAppPurchaseConstants.coinsPerPost;

  /// Add coins to FiddleTalk account (from purchase)
  Future<void> addCoins(int fiddletalkCoinAmount) async {
    if (fiddletalkCoinAmount <= 0) return;
    
    _fiddletalkCurrentCoins += fiddletalkCoinAmount;
    await _saveFiddleTalkDataToLocal();
    notifyListeners();
    
    debugPrint('FiddleTalk: Added $fiddletalkCoinAmount coins. Current balance: $_fiddletalkCurrentCoins');
  }

  /// Consume coins or free quota for creating a FiddleTalk post
  Future<bool> consumeForFiddleTalkPost() async {
    // 优先使用免费发帖额度
    if (_fiddletalkFreePostQuota > 0) {
      _fiddletalkFreePostQuota--;
      await _saveFiddleTalkDataToLocal();
      notifyListeners();
      debugPrint('FiddleTalk: Used free post quota. Remaining free posts: $_fiddletalkFreePostQuota');
      return true;
    }
    
    // 然后使用金币
    if (_fiddletalkCurrentCoins >= FiddleTalkInAppPurchaseConstants.coinsPerPost) {
      _fiddletalkCurrentCoins -= FiddleTalkInAppPurchaseConstants.coinsPerPost;
      await _saveFiddleTalkDataToLocal();
      notifyListeners();
      debugPrint('FiddleTalk: Used ${FiddleTalkInAppPurchaseConstants.coinsPerPost} coins for post. Remaining coins: $_fiddletalkCurrentCoins');
      return true;
    }
    
    debugPrint('FiddleTalk: Insufficient coins and free quota for creating post');
    return false;
  }

  /// Get remaining posts user can create (free quota + coins)
  int get remainingPosts {
    int fiddletalkFromFreeQuota = _fiddletalkFreePostQuota;
    int fiddletalkFromCoins = (_fiddletalkCurrentCoins / FiddleTalkInAppPurchaseConstants.coinsPerPost).floor();
    return fiddletalkFromFreeQuota + fiddletalkFromCoins;
  }

  /// Reset free post quota (for testing or special events)
  Future<void> resetFiddleTalkFreePostQuota() async {
    _fiddletalkFreePostQuota = 3;
    await _saveFiddleTalkDataToLocal();
    notifyListeners();
    debugPrint('FiddleTalk: Reset free post quota to 3');
  }

  /// Clear all FiddleTalk data (for testing)
  Future<void> clearAllFiddleTalkData() async {
    _fiddletalkCurrentCoins = 0;
    _fiddletalkFreePostQuota = 3;
    await _saveFiddleTalkDataToLocal();
    notifyListeners();
    debugPrint('FiddleTalk: Cleared all purchase data');
  }
}

/// FiddleTalk In-app purchase constants
class FiddleTalkInAppPurchaseConstants {
  static const int coinsPerPost = 10; // 每创建一个FiddleTalk帖子需要10金币
} 