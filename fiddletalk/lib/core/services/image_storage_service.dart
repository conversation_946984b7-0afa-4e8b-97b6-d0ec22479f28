import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service for managing image storage in the app
class ImageStorageService {
  static final ImageStorageService _instance = ImageStorageService._internal();
  factory ImageStorageService() => _instance;
  ImageStorageService._internal();

  /// Get the directory for storing user images
  Future<Directory> _getUserImagesDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final userImagesDir = Directory(path.join(appDir.path, 'user_images'));

    if (!await userImagesDir.exists()) {
      await userImagesDir.create(recursive: true);
    }

    return userImagesDir;
  }

  /// Get the directory for storing post images
  Future<Directory> _getPostImagesDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final postImagesDir = Directory(path.join(appDir.path, 'user_images', 'posts'));

    if (!await postImagesDir.exists()) {
      await postImagesDir.create(recursive: true);
    }

    return postImagesDir;
  }

  /// Save user avatar image and return the permanent path
  Future<String?> saveAvatarImage(File imageFile) async {
    try {
      final userImagesDir = await _getUserImagesDirectory();

      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = 'avatar_$timestamp$extension';

      // Create the permanent file path
      final permanentPath = path.join(userImagesDir.path, fileName);

      // Copy the image to permanent location
      final permanentFile = await imageFile.copy(permanentPath);

      debugPrint('Avatar image saved to: ${permanentFile.path}');
      return permanentFile.path;
    } catch (e) {
      debugPrint('Error saving avatar image: $e');
      return null;
    }
  }

  /// Save post image and return the permanent path
  Future<String?> savePostImage(File imageFile) async {
    try {
      final postImagesDir = await _getPostImagesDirectory();

      // Generate unique filename with timestamp and random suffix
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final randomSuffix = (timestamp % 10000).toString().padLeft(4, '0');
      final extension = path.extension(imageFile.path);
      final fileName = 'post_${timestamp}_$randomSuffix$extension';

      // Create the permanent file path
      final permanentPath = path.join(postImagesDir.path, fileName);

      // Copy the image to permanent location
      final permanentFile = await imageFile.copy(permanentPath);

      debugPrint('Post image saved to: ${permanentFile.path}');
      return permanentFile.path;
    } catch (e) {
      debugPrint('Error saving post image: $e');
      return null;
    }
  }

  /// Delete old avatar image if it exists
  Future<void> deleteOldAvatar(String? oldAvatarPath) async {
    if (oldAvatarPath == null || oldAvatarPath.startsWith('assets/')) {
      return; // Don't delete asset images
    }

    try {
      final file = File(oldAvatarPath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('Old avatar deleted: $oldAvatarPath');
      }
    } catch (e) {
      debugPrint('Error deleting old avatar: $e');
    }
  }

  /// Update user avatar with proper cleanup
  Future<String?> updateUserAvatar(File newImageFile, String? currentAvatarPath) async {
    try {
      // Save the new avatar
      final newAvatarPath = await saveAvatarImage(newImageFile);
      
      if (newAvatarPath != null) {
        // Delete the old avatar if it's not an asset
        await deleteOldAvatar(currentAvatarPath);
        return newAvatarPath;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error updating user avatar: $e');
      return null;
    }
  }

  /// Check if an image file exists
  Future<bool> imageExists(String imagePath) async {
    if (imagePath.startsWith('assets/')) {
      return true; // Asset images always exist
    }
    
    try {
      final file = File(imagePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Get image file size in bytes
  Future<int?> getImageSize(String imagePath) async {
    if (imagePath.startsWith('assets/')) {
      return null; // Can't get size of asset images
    }
    
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        return await file.length();
      }
      return null;
    } catch (e) {
      debugPrint('Error getting image size: $e');
      return null;
    }
  }

  /// Clean up unused avatar images (call this periodically)
  Future<void> cleanupUnusedImages() async {
    try {
      final userImagesDir = await _getUserImagesDirectory();
      final files = await userImagesDir.list().toList();

      // Get current time
      final now = DateTime.now();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          final daysSinceModified = now.difference(stat.modified).inDays;

          // Delete files older than 30 days (you can adjust this)
          if (daysSinceModified > 30) {
            await file.delete();
            debugPrint('Cleaned up old image: ${file.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error during cleanup: $e');
    }
  }

  /// Validate and fix image paths in posts
  Future<List<String>> validateAndFixImagePaths(List<String> imagePaths) async {
    final validPaths = <String>[];

    for (final imagePath in imagePaths) {
      if (imagePath.startsWith('assets/')) {
        // Asset images are always valid
        validPaths.add(imagePath);
      } else if (imagePath.startsWith('/')) {
        // Check if local file exists
        final exists = await imageExists(imagePath);
        if (exists) {
          validPaths.add(imagePath);
        } else {
          debugPrint('Invalid image path removed: $imagePath');
        }
      } else if (imagePath.startsWith('http')) {
        // Network images are assumed valid
        validPaths.add(imagePath);
      }
    }

    return validPaths;
  }

  /// Get all stored image files
  Future<List<File>> getAllStoredImages() async {
    try {
      final userImagesDir = await _getUserImagesDirectory();
      final files = await userImagesDir.list(recursive: true).toList();

      return files.whereType<File>().toList();
    } catch (e) {
      debugPrint('Error getting stored images: $e');
      return [];
    }
  }

  /// Get the total size of all stored images
  Future<int> getTotalStorageSize() async {
    try {
      final userImagesDir = await _getUserImagesDirectory();
      final files = await userImagesDir.list().toList();
      
      int totalSize = 0;
      for (final file in files) {
        if (file is File) {
          final size = await file.length();
          totalSize += size;
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('Error calculating storage size: $e');
      return 0;
    }
  }

  /// Format bytes to human readable string
  String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
