import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';

/// 用户协议服务
/// 管理用户是否已同意隐私协议和用户协议
class AgreementService extends ChangeNotifier {
  static final AgreementService _instance = AgreementService._internal();
  factory AgreementService() => _instance;
  AgreementService._internal();

  Box<dynamic>? _agreementBox;
  bool _isInitialized = false;

  // 协议URL
  static const String privacyPolicyUrl = 'https://1drv.ms/w/c/7d8ba11f366a0477/EfxpTcT0j3dDk5o7yjKcRo4BGSzvkx20Sf25MX4Fb5K8YQ?e=KeIftN';
  static const String userAgreementUrl = 'https://1drv.ms/w/c/7d8ba11f366a0477/EQheJgu1NPVCqGNegHVUeGMBTR49J2aZUEtUctvG-l1evg?e=ealdWF';

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _agreementBox = await Hive.openBox('user_agreements');
      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing AgreementService: $e');
    }
  }

  /// 检查用户是否已同意协议
  Future<bool> hasUserAgreedToTerms() async {
    await initialize();
    return _agreementBox?.get('agreed_to_terms', defaultValue: false) ?? false;
  }

  /// 设置用户已同意协议
  Future<void> setUserAgreedToTerms() async {
    await initialize();
    await _agreementBox?.put('agreed_to_terms', true);
    await _agreementBox?.put('agreement_date', DateTime.now().toIso8601String());
    notifyListeners();
  }

  /// 获取协议同意时间
  Future<DateTime?> getAgreementDate() async {
    await initialize();
    final dateString = _agreementBox?.get('agreement_date');
    if (dateString != null) {
      return DateTime.tryParse(dateString);
    }
    return null;
  }

  /// 重置协议状态（用于测试）
  Future<void> resetAgreementStatus() async {
    await initialize();
    await _agreementBox?.delete('agreed_to_terms');
    await _agreementBox?.delete('agreement_date');
    notifyListeners();
  }
} 