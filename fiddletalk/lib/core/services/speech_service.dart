import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';

/// Speech to text service with real-time voice recognition
class SpeechService {
  static final SpeechService _instance = SpeechService._internal();
  factory SpeechService() => _instance;
  SpeechService._internal();

  final stt.SpeechToText _speech = stt.SpeechToText();

  bool _isInitialized = false;
  bool _isListening = false;
  String _lastWords = '';
  double _soundLevel = 0.0;

  /// Initialize speech recognition with automatic permission request
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _isInitialized = await _speech.initialize(
        onError: (error) {
          print('Speech recognition error: $error');
        },
        onStatus: (status) {
          print('Speech recognition status: $status');
        },
      );
      return _isInitialized;
    } catch (e) {
      print('Failed to initialize speech recognition: $e');
      return false;
    }
  }

  /// Check microphone permission only when needed
  Future<bool> _checkMicrophonePermission(BuildContext context) async {
    final status = await Permission.microphone.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      final result = await Permission.microphone.request();
      if (!result.isGranted) {
        _showErrorMessage(context, 'No microphone privilege');
        return false;
      }
      return true;
    }

    if (status.isPermanentlyDenied) {
      _showErrorMessage(context, 'No microphone privilege');
      return false;
    }

    return false;
  }

  /// Start real-time speech recognition (English only)
  Future<bool> startRealTimeListening({
    required BuildContext context,
    required Function(String) onResult,
    required Function(bool) onListeningStateChanged,
    required Function(double) onSoundLevelChange,
  }) async {
    try {
      // Check microphone permission first
      final hasPermission = await _checkMicrophonePermission(context);
      if (!hasPermission) {
        return false;
      }

      // Initialize speech recognition (this will auto-request speech permission)
      if (!_isInitialized) {
        final initialized = await initialize();
        if (!initialized) {
          _showErrorMessage(context, 'No speech recognition privilege');
          return false;
        }
      }

      // Check if speech recognition is available
      if (!_speech.isAvailable) {
        _showErrorMessage(context, 'Speech recognition is not available on this device');
        return false;
      }

      // Start listening with real-time features
      _isListening = true;
      onListeningStateChanged(true);

      await _speech.listen(
        onResult: (result) {
          _lastWords = result.recognizedWords;
          onResult(_lastWords);
        },
        listenFor: const Duration(minutes: 10), // Support up to 10 minutes
        pauseFor: const Duration(seconds: 3), // Auto pause after 3 seconds of silence
        partialResults: true, // Enable real-time updates
        localeId: 'en_US', // English only
        onSoundLevelChange: (level) {
          _soundLevel = level;
          onSoundLevelChange(level);
        },
        cancelOnError: true,
        listenMode: stt.ListenMode.confirmation,
      );

      return true;
    } catch (e) {
      _isListening = false;
      onListeningStateChanged(false);
      _showErrorMessage(context, 'Failed to start speech recognition');
      return false;
    }
  }

  /// Stop listening
  Future<void> stopListening({
    required Function(bool) onListeningStateChanged,
  }) async {
    try {
      await _speech.stop();
      _isListening = false;
      onListeningStateChanged(false);

      // Haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      print('Error stopping speech recognition: $e');
    }
  }

  /// Cancel listening
  Future<void> cancelListening({
    required Function(bool) onListeningStateChanged,
  }) async {
    try {
      await _speech.cancel();
      _isListening = false;
      onListeningStateChanged(false);

      // Haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      print('Error canceling speech recognition: $e');
    }
  }

  /// Get available locales
  Future<List<stt.LocaleName>> getAvailableLocales() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _speech.locales();
  }

  /// Check if currently listening
  bool get isListening => _isListening && _speech.isListening;

  /// Check if speech recognition is available
  bool get isAvailable => _speech.isAvailable;

  /// Get last recognized words
  String get lastWords => _lastWords;

  /// Get current sound level
  double get soundLevel => _soundLevel;

  /// Show simple error message (English only, no guidance to settings)
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    // Haptic feedback for error
    HapticFeedback.heavyImpact();
  }



  /// Dispose resources
  void dispose() {
    _speech.cancel();
  }
}
