import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

/// Permission service for handling various app permissions
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Request camera permission
  Future<bool> requestCameraPermission(BuildContext context) async {
    final status = await Permission.camera.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      final result = await Permission.camera.request();
      return result.isGranted;
    }
    
    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog(
        context,
        'Camera Permission Required',
        'Camera access is needed to take photos. Please enable camera permission in settings.',
      );
      return false;
    }
    
    return false;
  }

  /// Request photo library permission
  Future<bool> requestPhotoLibraryPermission(BuildContext context) async {
    final status = await Permission.photos.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      final result = await Permission.photos.request();
      return result.isGranted;
    }
    
    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog(
        context,
        'Photo Library Permission Required',
        'Photo library access is needed to select images. Please enable photo library permission in settings.',
      );
      return false;
    }
    
    return false;
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission(BuildContext context) async {
    final status = await Permission.microphone.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      final result = await Permission.microphone.request();
      return result.isGranted;
    }
    
    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog(
        context,
        'Microphone Permission Required',
        'Microphone access is needed for voice input. Please enable microphone permission in settings.',
      );
      return false;
    }
    
    return false;
  }

  /// Request speech recognition permission
  Future<bool> requestSpeechPermission(BuildContext context) async {
    final status = await Permission.speech.status;
    
    if (status.isGranted) {
      return true;
    }
    
    if (status.isDenied) {
      final result = await Permission.speech.request();
      return result.isGranted;
    }
    
    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog(
        context,
        'Speech Recognition Permission Required',
        'Speech recognition access is needed for voice to text conversion. Please enable speech recognition permission in settings.',
      );
      return false;
    }
    
    return false;
  }

  /// Show permission denied dialog
  void _showPermissionDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: AppTextStyles.headline6.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          content: Text(
            message,
            style: AppTextStyles.body1.copyWith(
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: AppTextStyles.button.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show no permission toast message
  void showNoPermissionMessage(BuildContext context, String permissionType) {
    String message;
    switch (permissionType.toLowerCase()) {
      case 'camera':
        message = 'No camera privileges';
        break;
      case 'photos':
      case 'gallery':
        message = 'No album access';
        break;
      case 'microphone':
        message = 'No microphone privilege';
        break;
      case 'speech':
        message = 'No speech recognition privilege';
        break;
      default:
        message = 'Permission denied';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: AppTextStyles.body2.copyWith(
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Check if camera permission is granted
  Future<bool> isCameraPermissionGranted() async {
    return await Permission.camera.isGranted;
  }

  /// Check if photo library permission is granted
  Future<bool> isPhotoLibraryPermissionGranted() async {
    return await Permission.photos.isGranted;
  }

  /// Check if microphone permission is granted
  Future<bool> isMicrophonePermissionGranted() async {
    return await Permission.microphone.isGranted;
  }

  /// Check if speech recognition permission is granted
  Future<bool> isSpeechPermissionGranted() async {
    return await Permission.speech.isGranted;
  }

  /// Request camera permission and handle result
  Future<bool> handleCameraPermission(BuildContext context) async {
    if (await isCameraPermissionGranted()) {
      return true;
    }

    final granted = await requestCameraPermission(context);
    if (!granted) {
      showNoPermissionMessage(context, 'camera');
    }
    return granted;
  }

  /// Request photo library permission and handle result
  Future<bool> handlePhotoLibraryPermission(BuildContext context) async {
    if (await isPhotoLibraryPermissionGranted()) {
      return true;
    }

    final granted = await requestPhotoLibraryPermission(context);
    if (!granted) {
      showNoPermissionMessage(context, 'photos');
    }
    return granted;
  }

  /// Request microphone permission and handle result
  Future<bool> handleMicrophonePermission(BuildContext context) async {
    if (await isMicrophonePermissionGranted()) {
      return true;
    }

    final granted = await requestMicrophonePermission(context);
    if (!granted) {
      showNoPermissionMessage(context, 'microphone');
    }
    return granted;
  }

  /// Request speech recognition permission and handle result
  Future<bool> handleSpeechPermission(BuildContext context) async {
    if (await isSpeechPermissionGranted()) {
      return true;
    }

    final granted = await requestSpeechPermission(context);
    if (!granted) {
      showNoPermissionMessage(context, 'speech');
    }
    return granted;
  }

  /// Request both microphone and speech permissions for voice input (step by step)
  Future<bool> handleVoiceInputPermissions(BuildContext context) async {
    // Step 1: Request microphone permission first
    final micGranted = await handleMicrophonePermission(context);
    if (!micGranted) {
      return false;
    }

    // Step 2: Request speech recognition permission
    final speechGranted = await handleSpeechPermission(context);
    return speechGranted;
  }

  /// Request microphone permission with dialog
  Future<bool> requestMicrophonePermissionWithDialog(BuildContext context) async {
    final userChoice = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Microphone Permission',
            style: AppTextStyles.headline6.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'This app needs microphone access to record your voice for voice input functionality.',
            style: AppTextStyles.body1.copyWith(
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Deny',
                style: AppTextStyles.button.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Allow',
                style: AppTextStyles.button.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (userChoice == true) {
      final granted = await requestMicrophonePermission(context);
      if (!granted) {
        showNoPermissionMessage(context, 'microphone');
      }
      return granted;
    }
    return false;
  }

  /// Request speech recognition permission with dialog
  Future<bool> requestSpeechPermissionWithDialog(BuildContext context) async {
    final userChoice = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Speech Recognition Permission',
            style: AppTextStyles.headline6.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'This app needs speech recognition access to convert your voice to text.',
            style: AppTextStyles.body1.copyWith(
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Deny',
                style: AppTextStyles.button.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Allow',
                style: AppTextStyles.button.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (userChoice == true) {
      final granted = await requestSpeechPermission(context);
      if (!granted) {
        showNoPermissionMessage(context, 'speech');
      }
      return granted;
    }
    return false;
  }

  /// Handle voice input permissions with step-by-step dialogs
  Future<bool> handleVoiceInputPermissionsWithDialogs(BuildContext context) async {
    try {
      // Step 1: Check and request microphone permission
      print('Checking microphone permission...');
      final micStatus = await Permission.microphone.status;
      print('Microphone permission status: $micStatus');

      if (!micStatus.isGranted) {
        print('Requesting microphone permission...');

        // Show custom dialog first
        final userWantsMic = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Microphone Permission Required',
                style: AppTextStyles.headline6.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: Text(
                'This app needs microphone access to record your voice for voice input functionality.',
                style: AppTextStyles.body1.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Deny',
                    style: AppTextStyles.button.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    'Allow',
                    style: AppTextStyles.button.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ?? false;

        if (!userWantsMic) {
          print('User denied microphone permission in dialog');
          return false;
        }

        // Request actual permission
        final micResult = await Permission.microphone.request();
        print('Microphone permission result: $micResult');

        if (!micResult.isGranted) {
          showNoPermissionMessage(context, 'microphone');
          return false;
        }
      }

      // Step 2: Check and request speech recognition permission
      print('Checking speech recognition permission...');
      final speechStatus = await Permission.speech.status;
      print('Speech recognition permission status: $speechStatus');

      if (!speechStatus.isGranted) {
        print('Requesting speech recognition permission...');

        // Show custom dialog first
        final userWantsSpeech = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Speech Recognition Permission Required',
                style: AppTextStyles.headline6.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: Text(
                'This app needs speech recognition access to convert your voice to text (English only).',
                style: AppTextStyles.body1.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Deny',
                    style: AppTextStyles.button.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    'Allow',
                    style: AppTextStyles.button.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ?? false;

        if (!userWantsSpeech) {
          print('User denied speech recognition permission in dialog');
          return false;
        }

        // Request actual permission
        final speechResult = await Permission.speech.request();
        print('Speech recognition permission result: $speechResult');

        if (!speechResult.isGranted) {
          showNoPermissionMessage(context, 'speech');
          return false;
        }
      }

      print('All voice input permissions granted successfully');
      return true;
    } catch (e) {
      print('Error requesting voice input permissions: $e');
      return false;
    }
  }
}
