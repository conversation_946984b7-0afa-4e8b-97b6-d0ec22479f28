import '../../shared/models/user_model.dart';

/// 用户服务 - 管理当前用户信息
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  // 当前用户信息（模拟数据）
  static final UserModel _currentUser = UserModel(
    id: 'current_user_id',
    username: 'violin_enthusiast',
    nickname: 'Violin Enthusiast',
    avatar: 'assets/actor/girl.png',
    bio: '3 years of learning, love classical music, enjoy sharing playing insights',
    email: '<EMAIL>',
    level: 2,
    coins: 1250,
    followersCount: 128,
    followingCount: 89,
    postsCount: 45,
    badges: ['Technique Sharing Expert', 'Active User'],
    createdAt: DateTime.now().subtract(const Duration(days: 365 * 3)),
    updatedAt: DateTime.now(),
    isVerified: false,
    learningYears: '3 years',
    specialtyStyle: 'Classical Music',
    personalGoal: 'Play Bach\'s works fluently',
    isOnline: true,
    lastActiveAt: DateTime.now(),
  );

  /// 获取当前用户信息
  UserModel get currentUser => _currentUser;

  /// 获取当前用户ID
  String get currentUserId => _currentUser.id;

  /// 获取当前用户昵称
  String get currentUserNickname => _currentUser.nickname;

  /// 获取当前用户头像
  String? get currentUserAvatar => _currentUser.avatar;

  /// 检查是否为当前用户
  bool isCurrentUser(String userId) {
    return userId == _currentUser.id;
  }

  /// 更新用户信息
  void updateUser({
    String? nickname,
    String? avatar,
    String? bio,
    String? learningYears,
    String? specialtyStyle,
    String? personalGoal,
  }) {
    // 这里应该调用API更新用户信息
    // 目前只是模拟更新
    if (nickname != null) _currentUser.nickname = nickname;
    if (avatar != null) _currentUser.avatar = avatar;
    if (bio != null) _currentUser.bio = bio;
    if (learningYears != null) _currentUser.learningYears = learningYears;
    if (specialtyStyle != null) _currentUser.specialtyStyle = specialtyStyle;
    if (personalGoal != null) _currentUser.personalGoal = personalGoal;
    
    _currentUser.updatedAt = DateTime.now();
  }
}
