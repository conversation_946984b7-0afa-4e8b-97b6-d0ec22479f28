import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'permission_service.dart';

/// Image picker service with permission handling
class ImagePickerService {
  static final ImagePickerService _instance = ImagePickerService._internal();
  factory ImagePickerService() => _instance;
  ImagePickerService._internal();

  final ImagePicker _picker = ImagePicker();
  final PermissionService _permissionService = PermissionService();

  /// Pick image from gallery with permission handling
  Future<File?> pickFromGallery(BuildContext context) async {
    try {
      // Request permission first
      final hasPermission = await _permissionService.handlePhotoLibraryPermission(context);
      if (!hasPermission) {
        return null;
      }

      // Pick image
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _showErrorMessage(context, 'Failed to pick image from gallery');
      return null;
    }
  }

  /// Pick image from camera with permission handling
  Future<File?> pickFromCamera(BuildContext context) async {
    try {
      // Request permission first
      final hasPermission = await _permissionService.handleCameraPermission(context);
      if (!hasPermission) {
        return null;
      }

      // Take photo
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _showErrorMessage(context, 'Failed to take photo');
      return null;
    }
  }

  /// Show image source selection dialog
  Future<File?> showImageSourceDialog(BuildContext context) async {
    return await showDialog<File?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Select Image Source',
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.blue),
                title: const Text('Photo Library'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickFromGallery(context);
                  if (context.mounted) {
                    Navigator.of(context).pop(file);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.blue),
                title: const Text('Camera'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final file = await pickFromCamera(context);
                  if (context.mounted) {
                    Navigator.of(context).pop(file);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Pick multiple images from gallery
  Future<List<File>> pickMultipleFromGallery(BuildContext context, {int maxImages = 5}) async {
    try {
      // Request permission first
      final hasPermission = await _permissionService.handlePhotoLibraryPermission(context);
      if (!hasPermission) {
        return [];
      }

      // Pick multiple images
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      // Limit the number of images
      final limitedImages = images.take(maxImages).toList();
      
      return limitedImages.map((xFile) => File(xFile.path)).toList();
    } catch (e) {
      _showErrorMessage(context, 'Failed to pick images from gallery');
      return [];
    }
  }

  /// Show error message
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Validate image file
  bool isValidImageFile(File file) {
    final extension = file.path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Get image file size in MB
  double getImageSizeInMB(File file) {
    final bytes = file.lengthSync();
    return bytes / (1024 * 1024);
  }

  /// Check if image size is within limit
  bool isImageSizeValid(File file, {double maxSizeMB = 10.0}) {
    return getImageSizeInMB(file) <= maxSizeMB;
  }

  /// Compress image if needed
  Future<File?> compressImageIfNeeded(File file, {double maxSizeMB = 5.0}) async {
    try {
      if (isImageSizeValid(file, maxSizeMB: maxSizeMB)) {
        return file;
      }

      // For now, just return the original file
      // In a real app, you would use image compression libraries
      return file;
    } catch (e) {
      return null;
    }
  }
}
