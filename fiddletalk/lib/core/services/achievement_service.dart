import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../shared/models/achievement_model.dart';

/// Achievement service for managing user achievements
class AchievementService extends ChangeNotifier {
  static final AchievementService _instance = AchievementService._internal();
  factory AchievementService() => _instance;
  AchievementService._internal() {
    _initializeService();
  }

  // Hive box for storing achievement data
  Box<AchievementModel>? _achievementsBox;
  Box<dynamic>? _progressBox;

  // Achievement lists
  final List<AchievementModel> _achievements = [];
  bool _isInitialized = false;

  // Progress counters
  int _totalPostsCount = 0;
  int _totalCommentsCount = 0;

  // Keys for progress storage
  static const String _postsCountKey = 'total_posts_count';
  static const String _commentsCountKey = 'total_comments_count';

  /// Initialize the achievement service
  Future<void> _initializeService() async {
    try {
      _achievementsBox = await Hive.openBox<AchievementModel>('achievements');
      _progressBox = await Hive.openBox('achievement_progress');
      
      await _loadProgressFromLocal();
      await _initializeDefaultAchievements();
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing AchievementService: $e');
    }
  }

  /// Load progress from local storage
  Future<void> _loadProgressFromLocal() async {
    if (_progressBox != null) {
      _totalPostsCount = _progressBox!.get(_postsCountKey, defaultValue: 0) as int;
      _totalCommentsCount = _progressBox!.get(_commentsCountKey, defaultValue: 0) as int;
    }
  }

  /// Save progress to local storage
  Future<void> _saveProgressToLocal() async {
    if (_progressBox != null) {
      await _progressBox!.put(_postsCountKey, _totalPostsCount);
      await _progressBox!.put(_commentsCountKey, _totalCommentsCount);
    }
  }

  /// Initialize default achievements
  Future<void> _initializeDefaultAchievements() async {
    if (_achievementsBox == null) return;

    // Check if achievements are already initialized
    if (_achievementsBox!.isNotEmpty) {
      _achievements.clear();
      _achievements.addAll(_achievementsBox!.values);
      await _updateAllAchievementsProgress();
      return;
    }

    // Create default achievements
    final defaultAchievements = _createDefaultAchievements();
    
    // Save to Hive and add to memory
    for (final achievement in defaultAchievements) {
      await _achievementsBox!.put(achievement.id, achievement);
      _achievements.add(achievement);
    }

    await _updateAllAchievementsProgress();
  }

  /// Create default achievements list
  List<AchievementModel> _createDefaultAchievements() {
    final now = DateTime.now();
    final achievements = <AchievementModel>[];

    // Post achievements
    final postCounts = [1, 10, 100, 1000, 10000];
    for (final count in postCounts) {
      achievements.add(AchievementModel(
        id: 'posts_$count',
        title: 'Post Creator ${_getPostTitle(count)}',
        description: 'Publish $count ${count == 1 ? 'post' : 'posts'}',
        icon: _getPostIcon(count),
        type: AchievementType.posts,
        targetCount: count,
        createdAt: now,
      ));
    }

    // Comment achievements
    final commentCounts = [1, 10, 100, 1000, 10000];
    for (final count in commentCounts) {
      achievements.add(AchievementModel(
        id: 'comments_$count',
        title: 'Active Commenter ${_getCommentTitle(count)}',
        description: 'Write $count ${count == 1 ? 'comment' : 'comments'}',
        icon: _getCommentIcon(count),
        type: AchievementType.comments,
        targetCount: count,
        createdAt: now,
      ));
    }

    return achievements;
  }

  /// Get post achievement title suffix
  String _getPostTitle(int count) {
    switch (count) {
      case 1: return 'Beginner';
      case 10: return 'Amateur';
      case 100: return 'Professional';
      case 1000: return 'Master';
      case 10000: return 'Legend';
      default: return 'Expert';
    }
  }

  /// Get comment achievement title suffix
  String _getCommentTitle(int count) {
    switch (count) {
      case 1: return 'Newbie';
      case 10: return 'Participant';
      case 100: return 'Contributor';
      case 1000: return 'Expert';
      case 10000: return 'Legend';
      default: return 'Pro';
    }
  }

  /// Get post achievement icon
  String _getPostIcon(int count) {
    switch (count) {
      case 1: return '📝';
      case 10: return '📄';
      case 100: return '📚';
      case 1000: return '🏆';
      case 10000: return '👑';
      default: return '⭐';
    }
  }

  /// Get comment achievement icon
  String _getCommentIcon(int count) {
    switch (count) {
      case 1: return '💬';
      case 10: return '🗨️';
      case 100: return '💭';
      case 1000: return '🎯';
      case 10000: return '🌟';
      default: return '⭐';
    }
  }

  /// Update all achievements progress
  Future<void> _updateAllAchievementsProgress() async {
    if (_achievementsBox == null) return;

    for (int i = 0; i < _achievements.length; i++) {
      final achievement = _achievements[i];
      int currentCount = 0;

      switch (achievement.type) {
        case AchievementType.posts:
          currentCount = _totalPostsCount;
          break;
        case AchievementType.comments:
          currentCount = _totalCommentsCount;
          break;
      }

      if (currentCount != achievement.currentCount) {
        final updatedAchievement = achievement.updateProgress(currentCount);
        _achievements[i] = updatedAchievement;
        await _achievementsBox!.put(achievement.id, updatedAchievement);

        // Show unlock notification if newly unlocked
        if (updatedAchievement.canUnlock && !achievement.isUnlocked) {
          _showAchievementUnlocked(updatedAchievement);
        }
      }
    }
  }

  /// Show achievement unlocked notification
  void _showAchievementUnlocked(AchievementModel achievement) {
    debugPrint('🎉 Achievement Unlocked: ${achievement.title}');
    // Here you could show a toast, dialog, or other notification
  }

  /// Record a new post
  Future<void> recordPost() async {
    _totalPostsCount++;
    await _saveProgressToLocal();
    await _updateAllAchievementsProgress();
    notifyListeners();
  }

  /// Record a new comment
  Future<void> recordComment() async {
    _totalCommentsCount++;
    await _saveProgressToLocal();
    await _updateAllAchievementsProgress();
    notifyListeners();
  }

  // Getters
  bool get isInitialized => _isInitialized;
  List<AchievementModel> get achievements => List.from(_achievements);
  List<AchievementModel> get unlockedAchievements => _achievements.where((a) => a.isUnlocked).toList();
  List<AchievementModel> get lockedAchievements => _achievements.where((a) => !a.isUnlocked).toList();
  int get totalPostsCount => _totalPostsCount;
  int get totalCommentsCount => _totalCommentsCount;
  int get unlockedAchievementsCount => unlockedAchievements.length;
  int get totalAchievementsCount => _achievements.length;

  /// Get achievements by type
  List<AchievementModel> getAchievementsByType(AchievementType type) {
    return _achievements.where((a) => a.type == type).toList();
  }

  /// Get achievement by ID
  AchievementModel? getAchievementById(String id) {
    try {
      return _achievements.firstWhere((a) => a.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Reset all achievements (for testing)
  Future<void> resetAllAchievements() async {
    _totalPostsCount = 0;
    _totalCommentsCount = 0;
    await _saveProgressToLocal();
    
    if (_achievementsBox != null) {
      await _achievementsBox!.clear();
      _achievements.clear();
      await _initializeDefaultAchievements();
    }
    
    notifyListeners();
  }
} 