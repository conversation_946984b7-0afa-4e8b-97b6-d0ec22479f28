import 'dart:async';
import '../../shared/models/post_model.dart';

/// Global post state management service
/// Manages like and collect states across all screens
class PostStateService {
  static final PostStateService _instance = PostStateService._internal();
  factory PostStateService() => _instance;
  PostStateService._internal();

  // Stream controllers for state changes
  final StreamController<PostModel> _postUpdateController = StreamController<PostModel>.broadcast();
  
  // Current post states
  final Map<String, PostModel> _postStates = {};

  /// Stream of post updates
  Stream<PostModel> get postUpdates => _postUpdateController.stream;

  /// Initialize or update a post state
  void updatePostState(PostModel post) {
    _postStates[post.id] = post;
    _postUpdateController.add(post);
  }

  /// Get current post state
  PostModel? getPostState(String postId) {
    return _postStates[postId];
  }

  /// Toggle like state for a post
  void toggleLike(String postId) {
    final currentPost = _postStates[postId];
    if (currentPost != null) {
      final updatedPost = currentPost.copyWith(
        isLiked: !currentPost.isLiked,
        likesCount: currentPost.isLiked 
            ? currentPost.likesCount - 1 
            : currentPost.likesCount + 1,
      );
      updatePostState(updatedPost);
    }
  }

  /// Toggle collect state for a post
  void toggleCollect(String postId) {
    final currentPost = _postStates[postId];
    if (currentPost != null) {
      final updatedPost = currentPost.copyWith(
        isCollected: !currentPost.isCollected,
        collectionsCount: currentPost.isCollected 
            ? currentPost.collectionsCount - 1 
            : currentPost.collectionsCount + 1,
      );
      updatePostState(updatedPost);
    }
  }

  /// Initialize posts from a list
  void initializePosts(List<PostModel> posts) {
    for (final post in posts) {
      _postStates[post.id] = post;
    }
  }

  /// Get all liked posts
  List<PostModel> getLikedPosts() {
    return _postStates.values.where((post) => post.isLiked).toList();
  }

  /// Get all collected posts
  List<PostModel> getCollectedPosts() {
    return _postStates.values.where((post) => post.isCollected).toList();
  }

  /// Dispose resources
  void dispose() {
    _postUpdateController.close();
  }
}
