/// FiddleTalk Application Constants
class AppConstants {
  // App Information
  static const String appName = 'FiddleTalk';
  static const String appSlogan = 'Violin Community & Experience Sharing';
  static const String appVersion = '1.0.0';
  
  // Network Configuration
  static const String baseUrl = 'https://api.fiddletalk.com';
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userInfoKey = 'user_info';
  static const String settingsKey = 'app_settings';
  static const String cacheKey = 'app_cache';
  
  // Page Routes
  static const String splashRoute = '/splash';
  static const String homeRoute = '/home';
  static const String communityRoute = '/community';
  static const String profileRoute = '/profile';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String postDetailRoute = '/post-detail';
  static const String createPostRoute = '/create-post';
  static const String coinShopRoute = '/coin-shop';
  static const String settingsRoute = '/settings';
  
  // Content Categories
  static const List<String> contentCategories = [
    'Technique Tutorial',
    'Music Experience',
    'Equipment Review',
    'Creative Sharing',
    'Teaching Exchange',
  ];
  
  // Music Styles
  static const List<String> musicStyles = [
    'Classical',
    'Popular',
    'Film Score',
    'Original',
  ];

  // Playing Levels
  static const List<String> playingLevels = [
    'Beginner',
    'Intermediate',
    'Advanced',
  ];
  
  // Topic Tags
  static const List<String> topicTags = [
    'Vibrato Technique',
    'Spiccato Practice',
    'Position Shifting',
    'Double Stops',
    'Harmonics',
    'Original Violin Music',
    'Equipment Purchase',
    'String Maintenance',
    'Teaching Tips',
    'Performance Experience',
  ];

  // User Levels
  static const List<String> userLevels = [
    'Novice Player',
    'Intermediate Player',
    'Advanced Player',
    'Professional Player',
    'Master Player',
  ];
  
  // Badge Types
  static const List<String> badgeTypes = [
    'Technique Sharing Expert',
    'Creative Rising Star',
    'Helpful Member',
    'Active User',
    'Quality Content Creator',
    'Community Contributor',
  ];
  
  // Coin System
  static const int postCoinCost = 10; // Coins cost for posting
  static const int commentCoinReward = 1; // Coins reward for commenting
  static const int likeCoinReward = 1; // Coins reward for liking
  static const int shareCoinReward = 2; // Coins reward for sharing
  static const int dailySignInReward = 5; // Daily sign-in reward
  
  // Coin Packages
  static const Map<String, int> coinPackages = {
    'Basic Pack': 100,
    'Standard Pack': 500,
    'Premium Pack': 1000,
    'Ultimate Pack': 2000,
  };
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const int maxImagesPerPost = 9; // Max 9 images per post

  // Text Limits
  static const int maxPostTitleLength = 100;
  static const int maxPostContentLength = 5000;
  static const int maxCommentLength = 500;
  static const int maxBioLength = 200;
  
  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Cache Configuration
  static const int imageCacheMaxAge = 7 * 24 * 60 * 60; // 7 days
  static const int dataCacheMaxAge = 30 * 60; // 30 minutes

  // Animation Configuration
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);
  
  // Splash Animation Configuration
  static const Duration splashDuration = Duration(seconds: 3);
  static const String splashAnimationPath = 'assets/animations/splash.json';
  
  // Default Avatar
  static const String defaultAvatarPath = 'assets/images/default_avatar.png';

  // Placeholder Image
  static const String placeholderImagePath = 'assets/images/placeholder.png';

  // Error Image
  static const String errorImagePath = 'assets/images/error.png';

  // Empty State Image
  static const String emptyStatePath = 'assets/images/empty_state.png';
}
