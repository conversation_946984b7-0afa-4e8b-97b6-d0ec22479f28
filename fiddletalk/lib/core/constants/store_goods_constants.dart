/// 商城商品统一数据源 - 单一数据源原则
/// 所有商品码映射都从这里获取，避免硬编码分散在多个文件中

import '../../shared/models/fiddletalk_store_item_model.dart';

/// 商品数据模型
class SCGoods {
  final String code;
  final String exchangeCoin;
  final String price;
  final String tags;

  const SCGoods({
    required this.code,
    required this.exchangeCoin,
    required this.price,
    required this.tags,
  });

  /// 转换为FiddleTalkStoreItemModel
  FiddleTalkStoreItemModel toStoreItemModel() {
    return FiddleTalkStoreItemModel(
      code: code,
      price: double.parse(price),
      exchangeCoin: int.parse(exchangeCoin),
      tags: tags.isEmpty ? null : tags,
    );
  }

  /// 是否为促销商品
  bool get isPromotion => tags.contains('Big Deal');

  /// 获取显示名称
  String get displayName {
    final coinAmount = int.parse(exchangeCoin);
    if (coinAmount >= 10000) {
      return 'Premium Collection';
    } else if (coinAmount >= 5000) {
      return 'Professional Pack';
    } else if (coinAmount >= 1000) {
      return 'Advanced Pack';
    } else if (coinAmount >= 500) {
      return 'Standard Pack';
    } else {
      return 'Basic Pack';
    }
  }

  /// 获取描述
  String get description {
    if (isPromotion) {
      return 'Special promotional offer! Limited time deal with extra value.';
    }
    return 'Premium coins for FiddleTalk features and content creation.';
  }

  /// 获取图标路径
  String get iconPath {
    if (isPromotion) {
      return 'assets/icons/coin_premium.png';
    }
    
    final coinAmount = int.parse(exchangeCoin);
    if (coinAmount >= 10000) {
      return 'assets/icons/coin_premium.png';
    } else if (coinAmount >= 1000) {
      return 'assets/icons/coin_standard.png';
    } else {
      return 'assets/icons/coin_basic.png';
    }
  }
}

/// 商城商品常量类 - 统一数据源
class StoreGoodsConstants {
  /// 所有商品数据 - 单一数据源
  static const List<SCGoods> allGoods = [
    // 普通商品
    SCGoods(code: "311400", exchangeCoin: "100", price: "0.99", tags: ""),
    SCGoods(code: "395401", exchangeCoin: "399", price: "3.99", tags: ""),
    SCGoods(code: "395402", exchangeCoin: "499", price: "4.99", tags: ""),
    SCGoods(code: "395403", exchangeCoin: "999", price: "9.99", tags: ""),
    SCGoods(code: "395404", exchangeCoin: "1299", price: "12.99", tags: ""),
    SCGoods(code: "395405", exchangeCoin: "2500", price: "19.99", tags: ""),
    SCGoods(code: "395406", exchangeCoin: "3749", price: "29.99", tags: ""),
    SCGoods(code: "395407", exchangeCoin: "7000", price: "49.99", tags: ""),
    SCGoods(code: "395408", exchangeCoin: "15000", price: "99.99", tags: ""),
    
    // 促销商品
    SCGoods(code: "395409", exchangeCoin: "500", price: "1.99", tags: "Big Deal"),
    SCGoods(code: "395410", exchangeCoin: "1200", price: "4.99", tags: "Big Deal"),
    SCGoods(code: "395411", exchangeCoin: "2500", price: "11.99", tags: "Big Deal"),
    SCGoods(code: "395412", exchangeCoin: "7000", price: "34.99", tags: "Big Deal"),
    SCGoods(code: "395413", exchangeCoin: "10000", price: "49.99", tags: "Big Deal"),
    SCGoods(code: "395414", exchangeCoin: "15000", price: "79.99", tags: "Big Deal"),
    SCGoods(code: "395415", exchangeCoin: "17888", price: "99.99", tags: "Big Deal"),
  ];

  /// 获取所有商品
  static List<SCGoods> getAllGoods() => allGoods;

  /// 获取普通商品
  static List<SCGoods> getRegularGoods() => 
      allGoods.where((goods) => !goods.isPromotion).toList();

  /// 获取促销商品
  static List<SCGoods> getPromotionGoods() => 
      allGoods.where((goods) => goods.isPromotion).toList();

  /// 根据商品码获取商品
  static SCGoods? getGoodsByCode(String code) {
    try {
      return allGoods.firstWhere((goods) => goods.code == code);
    } catch (e) {
      return null;
    }
  }

  /// 获取所有商品码
  static Set<String> getAllCodes() => 
      allGoods.map((goods) => goods.code).toSet();

  /// 检查商品码是否存在
  static bool isValidCode(String code) => 
      allGoods.any((goods) => goods.code == code);

  /// 获取商品显示名称
  static String getDisplayName(String code) {
    final goods = getGoodsByCode(code);
    return goods?.displayName ?? 'Unknown Product';
  }

  /// 获取商品描述
  static String getDescription(String code) {
    final goods = getGoodsByCode(code);
    return goods?.description ?? 'Product description not available';
  }

  /// 获取商品图标路径
  static String getIconPath(String code) {
    final goods = getGoodsByCode(code);
    return goods?.iconPath ?? 'assets/icons/coin_default.png';
  }

  /// 检查是否为促销商品
  static bool isPromotionGoods(String code) {
    final goods = getGoodsByCode(code);
    return goods?.isPromotion ?? false;
  }

  /// 转换为FiddleTalkStoreItemModel列表
  static List<FiddleTalkStoreItemModel> toStoreItemModels() {
    return allGoods.map((goods) => goods.toStoreItemModel()).toList();
  }
}
