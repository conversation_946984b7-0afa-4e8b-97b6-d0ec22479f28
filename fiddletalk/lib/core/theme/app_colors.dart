import 'package:flutter/material.dart';

/// FiddleTalk应用配色方案
/// 基于小提琴优雅、古典的特性设计
class AppColors {
  // 主色调 - 深邃的勃艮第红
  static const Color primary = Color(0xFF800020);
  static const Color primaryLight = Color(0xFFA0002A);
  static const Color primaryDark = Color(0xFF600018);
  
  // 辅助主色 - 典雅棕色
  static const Color secondary = Color(0xFF654321);
  static const Color secondaryLight = Color(0xFF8B5A2B);
  static const Color secondaryDark = Color(0xFF4A3018);
  
  // 强调色 - 温暖金色
  static const Color accent = Color(0xFFB8860B);
  static const Color accentLight = Color(0xFFDAA520);
  static const Color accentDark = Color(0xFF996F08);
  
  // 互动色 - 明亮蓝色
  static const Color interactive = Color(0xFF007AFF);
  static const Color interactiveLight = Color(0xFF339FFF);
  static const Color interactiveDark = Color(0xFF0056CC);
  
  // 活力色 - 橙色
  static const Color vibrant = Color(0xFFFF8C00);
  static const Color vibrantLight = Color(0xFFFFA533);
  static const Color vibrantDark = Color(0xFFCC7000);
  
  // 背景色 - 米白色系
  static const Color background = Color(0xFFF5F5DC);
  static const Color backgroundLight = Color(0xFFFFFFF0);
  static const Color backgroundDark = Color(0xFFEEEED0);
  
  // 表面色
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8F8F8);
  
  // 文字色 - 深灰色系
  static const Color textPrimary = Color(0xFF4A4A4A);
  static const Color textSecondary = Color(0xFF696969);
  static const Color textTertiary = Color(0xFF999999);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // 分割线和边框 - 浅灰色
  static const Color divider = Color(0xFFE0E0E0);
  static const Color border = Color(0xFFD0D0D0);
  static const Color borderLight = Color(0xFFF0F0F0);
  
  // 状态色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // 透明度变体
  static Color primaryWithOpacity(double opacity) => primary.withOpacity(opacity);
  static Color secondaryWithOpacity(double opacity) => secondary.withOpacity(opacity);
  static Color accentWithOpacity(double opacity) => accent.withOpacity(opacity);
  static Color interactiveWithOpacity(double opacity) => interactive.withOpacity(opacity);
  
  // 渐变色
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // 毛玻璃效果色彩
  static Color glassBackground = Colors.white.withOpacity(0.1);
  static Color glassBorder = Colors.white.withOpacity(0.2);
  
  // 阴影色
  static Color shadowLight = Colors.black.withOpacity(0.1);
  static Color shadowMedium = Colors.black.withOpacity(0.2);
  static Color shadowDark = Colors.black.withOpacity(0.3);
}
