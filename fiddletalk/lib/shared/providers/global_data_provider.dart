import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../models/user_model.dart';
import '../../core/services/mock_data_service.dart';

/// 全局数据管理服务
/// 管理点赞、收藏、评论、关注、拉黑、举报等数据
class GlobalDataProvider extends ChangeNotifier {
  static final GlobalDataProvider _instance = GlobalDataProvider._internal();
  factory GlobalDataProvider() => _instance;
  GlobalDataProvider._internal();

  // Hive boxes
  late Box<PostModel> _postsBox;
  late Box<CommentModel> _commentsBox;
  late Box<UserModel> _usersBox;
  late Box<dynamic> _userActionsBox; // 存储用户行为数据

  // 内存缓存
  final Map<String, List<CommentModel>> _postComments = {};
  final Set<String> _likedPosts = {};
  final Set<String> _collectedPosts = {};
  final Set<String> _followedUsers = {};
  final Set<String> _blockedUsers = {};
  final Set<String> _reportedUsers = {};
  final Map<String, List<String>> _userReportReasons = {};
  final List<String> _browseHistory = []; // 浏览历史

  bool _isInitialized = false;

  /// 初始化数据服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 打开 Hive boxes
      _postsBox = await Hive.openBox<PostModel>('posts');
      _commentsBox = await Hive.openBox<CommentModel>('comments');
      _usersBox = await Hive.openBox<UserModel>('users');
      _userActionsBox = await Hive.openBox('user_actions');

      // 从本地存储加载用户行为数据
      await _loadUserActions();

      // 预加载评论数据
      await _preloadComments();

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing GlobalDataProvider: $e');
    }
  }

  /// 从本地存储加载用户行为数据
  Future<void> _loadUserActions() async {
    try {
      // 加载点赞数据
      final likedPosts = _userActionsBox.get('liked_posts', defaultValue: <String>[]);
      _likedPosts.addAll(List<String>.from(likedPosts));

      // 加载收藏数据
      final collectedPosts = _userActionsBox.get('collected_posts', defaultValue: <String>[]);
      _collectedPosts.addAll(List<String>.from(collectedPosts));

      // 加载关注数据
      final followedUsers = _userActionsBox.get('followed_users', defaultValue: <String>[]);
      _followedUsers.addAll(List<String>.from(followedUsers));

      // 加载拉黑数据
      final blockedUsers = _userActionsBox.get('blocked_users', defaultValue: <String>[]);
      _blockedUsers.addAll(List<String>.from(blockedUsers));

      // 加载举报数据
      final reportedUsers = _userActionsBox.get('reported_users', defaultValue: <String>[]);
      _reportedUsers.addAll(List<String>.from(reportedUsers));

      // 加载浏览历史
      final browseHistory = _userActionsBox.get('browse_history', defaultValue: <String>[]);
      _browseHistory.addAll(List<String>.from(browseHistory));

      // 加载举报原因
      final reportReasons = _userActionsBox.get('report_reasons', defaultValue: <String, dynamic>{});
      _userReportReasons.addAll(Map<String, List<String>>.from(
        reportReasons.map((key, value) => MapEntry(key, List<String>.from(value)))
      ));
    } catch (e) {
      debugPrint('Error loading user actions: $e');
    }
  }

  /// 保存用户行为数据到本地存储
  Future<void> _saveUserActions() async {
    try {
      await _userActionsBox.put('liked_posts', _likedPosts.toList());
      await _userActionsBox.put('collected_posts', _collectedPosts.toList());
      await _userActionsBox.put('followed_users', _followedUsers.toList());
      await _userActionsBox.put('blocked_users', _blockedUsers.toList());
      await _userActionsBox.put('reported_users', _reportedUsers.toList());
      await _userActionsBox.put('browse_history', _browseHistory);
      await _userActionsBox.put('report_reasons', _userReportReasons);
    } catch (e) {
      debugPrint('Error saving user actions: $e');
    }
  }

  // ==================== 点赞相关 ====================

  /// 切换帖子点赞状态
  Future<void> togglePostLike(String postId) async {
    if (_likedPosts.contains(postId)) {
      _likedPosts.remove(postId);
    } else {
      _likedPosts.add(postId);
    }
    await _saveUserActions();
    notifyListeners();
  }

  /// 检查帖子是否被点赞
  bool isPostLiked(String postId) {
    return _likedPosts.contains(postId);
  }

  /// 获取用户点赞的帖子列表
  List<String> get likedPostIds => _likedPosts.toList();

  // ==================== 收藏相关 ====================

  /// 切换帖子收藏状态
  Future<void> togglePostCollection(String postId) async {
    if (_collectedPosts.contains(postId)) {
      _collectedPosts.remove(postId);
    } else {
      _collectedPosts.add(postId);
    }
    await _saveUserActions();
    notifyListeners();
  }

  /// 检查帖子是否被收藏
  bool isPostCollected(String postId) {
    return _collectedPosts.contains(postId);
  }

  /// 获取用户收藏的帖子列表
  List<String> get collectedPostIds => _collectedPosts.toList();

  // ==================== 关注相关 ====================

  /// 关注用户
  Future<void> followUser(String userId) async {
    _followedUsers.add(userId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 取消关注用户
  Future<void> unfollowUser(String userId) async {
    _followedUsers.remove(userId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 切换用户关注状态
  Future<void> toggleUserFollow(String userId) async {
    if (_followedUsers.contains(userId)) {
      _followedUsers.remove(userId);
    } else {
      _followedUsers.add(userId);
    }
    await _saveUserActions();
    notifyListeners();
  }

  /// 检查用户是否被关注
  bool isUserFollowed(String userId) {
    return _followedUsers.contains(userId);
  }

  /// 获取关注的用户列表
  List<String> get followedUserIds => _followedUsers.toList();

  // ==================== 拉黑相关 ====================

  /// 拉黑用户
  Future<void> blockUser(String userId) async {
    _blockedUsers.add(userId);
    // 拉黑时自动取消关注
    _followedUsers.remove(userId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 解除拉黑
  Future<void> unblockUser(String userId) async {
    _blockedUsers.remove(userId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 检查用户是否被拉黑
  bool isUserBlocked(String userId) {
    return _blockedUsers.contains(userId);
  }

  /// 获取拉黑的用户列表
  List<String> get blockedUserIds => _blockedUsers.toList();

  // ==================== 举报相关 ====================

  /// 举报用户
  Future<void> reportUser(String userId, List<String> reasons) async {
    _reportedUsers.add(userId);
    _userReportReasons[userId] = reasons;
    // 举报时自动取消关注
    _followedUsers.remove(userId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 检查用户是否被举报
  bool isUserReported(String userId) {
    return _reportedUsers.contains(userId);
  }

  /// 获取举报的用户列表
  List<String> get reportedUserIds => _reportedUsers.toList();

  /// 获取用户举报原因
  List<String> getUserReportReasons(String userId) {
    return _userReportReasons[userId] ?? [];
  }

  // ==================== 内容过滤 ====================

  /// 检查用户是否应该被过滤（拉黑或举报）
  bool shouldFilterUser(String userId) {
    return _blockedUsers.contains(userId) || _reportedUsers.contains(userId);
  }

  /// 过滤帖子列表，移除被拉黑或举报用户的帖子
  List<PostModel> filterPosts(List<PostModel> posts) {
    return posts.where((post) => !shouldFilterUser(post.authorId)).toList();
  }

  /// 过滤用户点赞的帖子列表
  List<String> getFilteredLikedPosts() {
    return _likedPosts.where((postId) {
      // 这里需要根据postId获取作者ID，暂时返回所有
      // 在实际使用时需要传入帖子数据进行过滤
      return true;
    }).toList();
  }

  // ==================== 评论相关 ====================

  /// 添加评论
  Future<void> addComment(CommentModel comment) async {
    try {
      await _commentsBox.put(comment.id, comment);
      
      if (!_postComments.containsKey(comment.postId)) {
        _postComments[comment.postId] = [];
      }
      _postComments[comment.postId]!.add(comment);
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding comment: $e');
    }
  }

  /// 获取帖子的评论列表
  List<CommentModel> getPostComments(String postId) {
    if (_postComments.containsKey(postId)) {
      return _postComments[postId]!;
    }
    
    // 从本地存储加载评论
    final comments = _commentsBox.values
        .where((comment) => comment.postId == postId)
        .toList();
    
    _postComments[postId] = comments;
    return comments;
  }

  /// 更新评论
  Future<void> updateComment(CommentModel comment) async {
    try {
      // 确保数据库已初始化
      if (!_isInitialized) {
        await initialize();
      }

      // 保存到Hive数据库
      await _commentsBox.put(comment.id, comment);

      // 更新内存缓存
      if (_postComments.containsKey(comment.postId)) {
        final index = _postComments[comment.postId]!.indexWhere((c) => c.id == comment.id);
        if (index != -1) {
          _postComments[comment.postId]![index] = comment;
        } else {
          // 如果在缓存中找不到，添加到缓存
          _postComments[comment.postId]!.add(comment);
        }
      } else {
        // 如果该帖子的评论缓存不存在，创建新的缓存
        _postComments[comment.postId] = [comment];
      }

      debugPrint('Comment updated successfully: ${comment.id}');
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating comment: $e');
      // 重新抛出异常，让调用者知道操作失败
      rethrow;
    }
  }

  /// 删除评论
  Future<void> deleteComment(String commentId) async {
    try {
      final comment = _commentsBox.get(commentId);
      if (comment != null) {
        await _commentsBox.delete(commentId);

        if (_postComments.containsKey(comment.postId)) {
          _postComments[comment.postId]!.removeWhere((c) => c.id == commentId);
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting comment: $e');
    }
  }

  /// 获取帖子的评论数量（包括所有回复）
  int getPostCommentsCount(String postId) {
    final comments = getPostComments(postId);
    int totalCount = comments.length; // 主评论数量

    // 计算所有回复数量
    for (final comment in comments) {
      totalCount += comment.replies.length;
    }

    return totalCount;
  }

  /// 预加载评论数据
  Future<void> _preloadComments() async {
    try {
      // 检查是否已经有评论数据
      if (_commentsBox.isNotEmpty) {
        // 从本地存储加载评论到内存缓存
        for (final comment in _commentsBox.values) {
          if (!_postComments.containsKey(comment.postId)) {
            _postComments[comment.postId] = [];
          }
          _postComments[comment.postId]!.add(comment);
        }
        return;
      }

      // 如果没有评论数据，生成一些模拟数据
      final mockDataService = MockDataService();

      // 为前10个帖子生成评论
      for (int i = 0; i < 10; i++) {
        final postId = 'post_$i';
        final comments = await mockDataService.getCommentsForPost(postId);

        // 保存到本地存储
        for (final comment in comments) {
          await _commentsBox.put(comment.id, comment);
        }

        // 保存到内存缓存
        _postComments[postId] = comments;
      }
    } catch (e) {
      debugPrint('Error preloading comments: $e');
    }
  }

  // ==================== 浏览历史相关 ====================

  /// 添加到浏览历史
  Future<void> addToBrowseHistory(String postId) async {
    // 如果已存在，先移除再添加到最前面
    _browseHistory.remove(postId);
    _browseHistory.insert(0, postId);

    // 限制历史记录数量（最多保存100条）
    if (_browseHistory.length > 100) {
      _browseHistory.removeRange(100, _browseHistory.length);
    }

    await _saveUserActions();
    notifyListeners();
  }

  /// 获取浏览历史列表
  List<String> get browseHistoryIds => _browseHistory.toList();

  /// 清空浏览历史
  Future<void> clearBrowseHistory() async {
    _browseHistory.clear();
    await _saveUserActions();
    notifyListeners();
  }

  /// 从浏览历史中移除指定帖子
  Future<void> removeFromBrowseHistory(String postId) async {
    _browseHistory.remove(postId);
    await _saveUserActions();
    notifyListeners();
  }

  /// 清理资源
  Future<void> dispose() async {
    await _postsBox.close();
    await _commentsBox.close();
    await _usersBox.close();
    await _userActionsBox.close();
    super.dispose();
  }
}
