import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../core/services/in_app_purchase_service.dart';
import '../../core/services/mock_data_service.dart';
import '../providers/global_data_provider.dart';

/// User data provider for managing current user information
class UserDataProvider extends ChangeNotifier {
  static final UserDataProvider _instance = UserDataProvider._internal();
  factory UserDataProvider() => _instance;
  UserDataProvider._internal() {
    // 监听内购服务的变化
    InAppPurchaseService().addListener(_onPurchaseServiceChanged);
  }

  // Hive box for user data storage
  Box<dynamic>? _userDataBox;

  // Current user data
  Map<String, dynamic> _userData = {
    'id': 'current_user',
    'username': 'violin_enthusiast',
    'nickname': 'Violin Enthusiast',
    'avatar': 'assets/actor/boy.png',
    'bio': '3 years of learning, love classical music, enjoy sharing playing insights',
    'email': '<EMAIL>',
    'level': 2,
    'coins': 1200,
    'followersCount': 156,
    'followingCount': 89,
    'postsCount': 23,
    'badges': ['Active User', 'Music Lover'],
    'learningYears': '3 years',
    'specialtyStyle': 'Classical Music',
    'personalGoal': 'Play Bach\'s works fluently',
    'isVerified': false,
    'isOnline': true,
  };

  /// 内购服务变化监听器
  void _onPurchaseServiceChanged() {
    notifyListeners(); // 通知UI更新
  }

  /// Get current user data with updated coins and posts count
  Map<String, dynamic> get userData {
    final data = Map<String, dynamic>.from(_userData);
    // Update coins from InAppPurchaseService (real-time)
    final purchaseService = InAppPurchaseService();
    data['coins'] = purchaseService.currentCoins;
    
    // Update posts count from MockDataService (real-time)
    final mockDataService = MockDataService();
    data['postsCount'] = mockDataService.getUserCreatedPostsCount();
    
    // Update following count from GlobalDataProvider (real data)
    final globalDataProvider = GlobalDataProvider();
    data['followingCount'] = globalDataProvider.followedUserIds.length;
    
    return data;
  }

  /// Get current user ID
  String get currentUserId => _userData['id'] as String;

  /// Get current user avatar
  String? get currentUserAvatar => _userData['avatar'] as String?;

  /// Get current user nickname
  String get currentUserNickname => _userData['nickname'] as String;

  /// Update user data
  Future<void> updateUserData(Map<String, dynamic> newData) async {
    _userData.addAll(newData);
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update user avatar (simplified for preset avatars)
  Future<void> updateAvatar(String avatarPath) async {
    _userData['avatar'] = avatarPath;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update user nickname
  Future<void> updateNickname(String nickname) async {
    _userData['nickname'] = nickname;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update user bio
  Future<void> updateBio(String bio) async {
    _userData['bio'] = bio;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update learning years
  Future<void> updateLearningYears(String learningYears) async {
    _userData['learningYears'] = learningYears;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update specialty style
  Future<void> updateSpecialtyStyle(String specialtyStyle) async {
    _userData['specialtyStyle'] = specialtyStyle;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update personal goal
  Future<void> updatePersonalGoal(String personalGoal) async {
    _userData['personalGoal'] = personalGoal;
    _userData['updatedAt'] = DateTime.now().toIso8601String();
    await saveUserData();
    notifyListeners();
  }

  /// Update multiple fields at once
  Future<void> updateProfile({
    String? avatar,
    String? nickname,
    String? bio,
    String? learningYears,
    String? specialtyStyle,
    String? personalGoal,
  }) async {
    bool hasChanges = false;

    if (avatar != null && avatar != _userData['avatar']) {
      _userData['avatar'] = avatar;
      hasChanges = true;
    }

    if (nickname != null && nickname != _userData['nickname']) {
      _userData['nickname'] = nickname;
      hasChanges = true;
    }

    if (bio != null && bio != _userData['bio']) {
      _userData['bio'] = bio;
      hasChanges = true;
    }

    if (learningYears != null && learningYears != _userData['learningYears']) {
      _userData['learningYears'] = learningYears;
      hasChanges = true;
    }

    if (specialtyStyle != null && specialtyStyle != _userData['specialtyStyle']) {
      _userData['specialtyStyle'] = specialtyStyle;
      hasChanges = true;
    }

    if (personalGoal != null && personalGoal != _userData['personalGoal']) {
      _userData['personalGoal'] = personalGoal;
      hasChanges = true;
    }

    if (hasChanges) {
      // Update the updatedAt timestamp
      _userData['updatedAt'] = DateTime.now().toIso8601String();

      // Save to storage
      await saveUserData();

      // Notify listeners
      notifyListeners();
    }
  }

  /// Reset user data to default
  void resetUserData() {
    _userData = {
      'id': 'current_user',
      'username': 'violin_enthusiast',
      'nickname': 'Violin Enthusiast',
      'avatar': 'assets/images/1.png',
      'bio': '3 years of learning, love classical music, enjoy sharing playing insights',
      'email': '<EMAIL>',
      'level': 2,
      'coins': 1200,
      'followersCount': 156,
      'followingCount': 89,
      'postsCount': 23,
      'badges': ['Active User', 'Music Lover'],
      'learningYears': '3 years',
      'specialtyStyle': 'Classical Music',
      'personalGoal': 'Play Bach\'s works fluently',
      'isVerified': false,
      'isOnline': true,
    };
    notifyListeners();
  }

  /// Initialize the user data provider
  Future<void> initialize() async {
    try {
      _userDataBox = await Hive.openBox('user_data');
      await loadUserData();
      
      // Initialize GlobalDataProvider to ensure following data is available
      final globalDataProvider = GlobalDataProvider();
      await globalDataProvider.initialize();
    } catch (e) {
      debugPrint('Error initializing UserDataProvider: $e');
    }
  }

  /// Load user data from storage
  Future<void> loadUserData() async {
    try {
      if (_userDataBox == null) {
        await initialize();
      }

      final savedUserData = _userDataBox?.get('current_user_data');
      if (savedUserData != null && savedUserData is Map) {
        _userData = Map<String, dynamic>.from(savedUserData);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user data: $e');
      // Keep default data if loading fails
      notifyListeners();
    }
  }

  /// Save user data to storage
  Future<void> saveUserData() async {
    try {
      if (_userDataBox == null) {
        await initialize();
      }

      await _userDataBox?.put('current_user_data', _userData);
      debugPrint('User data saved successfully');
    } catch (e) {
      debugPrint('Error saving user data: $e');
    }
  }

  /// Check if user is current user
  bool isCurrentUser(String userId) {
    return userId == currentUserId;
  }

  /// Get user display name
  String get displayName => _userData['nickname'] as String;

  /// Get user email
  String get email => _userData['email'] as String;

  /// Get user level
  int get level => _userData['level'] as int;

  /// Get user coins
  int get coins => _userData['coins'] as int;

  /// Get followers count
  int get followersCount => _userData['followersCount'] as int;

  /// Get following count
  int get followingCount => _userData['followingCount'] as int;

  /// Get posts count
  int get postsCount => _userData['postsCount'] as int;

  /// Get user badges
  List<String> get badges => List<String>.from(_userData['badges'] as List);

  /// Get learning years
  String? get learningYears => _userData['learningYears'] as String?;

  /// Get specialty style
  String? get specialtyStyle => _userData['specialtyStyle'] as String?;

  /// Get personal goal
  String? get personalGoal => _userData['personalGoal'] as String?;

  /// Get user bio
  String? get bio => _userData['bio'] as String?;

  /// Check if user is verified
  bool get isVerified => _userData['isVerified'] as bool? ?? false;

  /// Check if user is online
  bool get isOnline => _userData['isOnline'] as bool? ?? false;
}
