// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PostModelAdapter extends TypeAdapter<PostModel> {
  @override
  final int typeId = 1;

  @override
  PostModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PostModel(
      id: fields[0] as String,
      title: fields[1] as String,
      content: fields[2] as String,
      images: (fields[3] as List).cast<String>(),
      category: fields[4] as String,
      musicStyle: fields[5] as String?,
      playingLevel: fields[6] as String?,
      tags: (fields[7] as List).cast<String>(),
      authorId: fields[8] as String,
      author: fields[9] as UserModel?,
      likesCount: fields[10] as int,
      commentsCount: fields[11] as int,
      collectionsCount: fields[12] as int,
      sharesCount: fields[13] as int,
      isLiked: fields[14] as bool,
      isCollected: fields[15] as bool,
      isPinned: fields[16] as bool,
      isHighlighted: fields[17] as bool,
      createdAt: fields[18] as DateTime,
      updatedAt: fields[19] as DateTime,
      viewsCount: fields[20] as int,
      isDeleted: fields[21] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PostModel obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.content)
      ..writeByte(3)
      ..write(obj.images)
      ..writeByte(4)
      ..write(obj.category)
      ..writeByte(5)
      ..write(obj.musicStyle)
      ..writeByte(6)
      ..write(obj.playingLevel)
      ..writeByte(7)
      ..write(obj.tags)
      ..writeByte(8)
      ..write(obj.authorId)
      ..writeByte(9)
      ..write(obj.author)
      ..writeByte(10)
      ..write(obj.likesCount)
      ..writeByte(11)
      ..write(obj.commentsCount)
      ..writeByte(12)
      ..write(obj.collectionsCount)
      ..writeByte(13)
      ..write(obj.sharesCount)
      ..writeByte(14)
      ..write(obj.isLiked)
      ..writeByte(15)
      ..write(obj.isCollected)
      ..writeByte(16)
      ..write(obj.isPinned)
      ..writeByte(17)
      ..write(obj.isHighlighted)
      ..writeByte(18)
      ..write(obj.createdAt)
      ..writeByte(19)
      ..write(obj.updatedAt)
      ..writeByte(20)
      ..write(obj.viewsCount)
      ..writeByte(21)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PostModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
