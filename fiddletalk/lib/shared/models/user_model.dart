import 'package:hive/hive.dart';

part 'user_model.g.dart';

@HiveType(typeId: 0)
class UserModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String username;

  @HiveField(2)
  String nickname;

  @HiveField(3)
  String? avatar;

  @HiveField(4)
  String? bio;

  @HiveField(5)
  String email;

  @HiveField(6)
  String? phone;

  @HiveField(7)
  int level;

  @HiveField(8)
  int coins;

  @HiveField(9)
  int followersCount;

  @HiveField(10)
  int followingCount;

  @HiveField(11)
  int postsCount;

  @HiveField(12)
  List<String> badges;

  @HiveField(13)
  DateTime createdAt;

  @HiveField(14)
  DateTime updatedAt;

  @HiveField(15)
  bool isVerified;

  @HiveField(16)
  String? learningYears; // 学琴年限

  @HiveField(17)
  String? specialtyStyle; // 擅长风格

  @HiveField(18)
  String? personalGoal; // 个人目标

  @HiveField(19)
  bool isOnline;

  @HiveField(20)
  DateTime? lastActiveAt;

  UserModel({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    this.bio,
    required this.email,
    this.phone,
    this.level = 0,
    this.coins = 0,
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.badges = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.learningYears,
    this.specialtyStyle,
    this.personalGoal,
    this.isOnline = false,
    this.lastActiveAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      nickname: json['nickname'] as String,
      avatar: json['avatar'] as String?,
      bio: json['bio'] as String?,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      level: json['level'] as int? ?? 0,
      coins: json['coins'] as int? ?? 0,
      followersCount: json['followers_count'] as int? ?? 0,
      followingCount: json['following_count'] as int? ?? 0,
      postsCount: json['posts_count'] as int? ?? 0,
      badges: List<String>.from(json['badges'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isVerified: json['is_verified'] as bool? ?? false,
      learningYears: json['learning_years'] as String?,
      specialtyStyle: json['specialty_style'] as String?,
      personalGoal: json['personal_goal'] as String?,
      isOnline: json['is_online'] as bool? ?? false,
      lastActiveAt: json['last_active_at'] != null
          ? DateTime.parse(json['last_active_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'avatar': avatar,
      'bio': bio,
      'email': email,
      'phone': phone,
      'level': level,
      'coins': coins,
      'followers_count': followersCount,
      'following_count': followingCount,
      'posts_count': postsCount,
      'badges': badges,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_verified': isVerified,
      'learning_years': learningYears,
      'specialty_style': specialtyStyle,
      'personal_goal': personalGoal,
      'is_online': isOnline,
      'last_active_at': lastActiveAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? username,
    String? nickname,
    String? avatar,
    String? bio,
    String? email,
    String? phone,
    int? level,
    int? coins,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    List<String>? badges,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    String? learningYears,
    String? specialtyStyle,
    String? personalGoal,
    bool? isOnline,
    DateTime? lastActiveAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      level: level ?? this.level,
      coins: coins ?? this.coins,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      badges: badges ?? this.badges,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      learningYears: learningYears ?? this.learningYears,
      specialtyStyle: specialtyStyle ?? this.specialtyStyle,
      personalGoal: personalGoal ?? this.personalGoal,
      isOnline: isOnline ?? this.isOnline,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }

  // 获取用户等级名称
  String get levelName {
    const levels = [
      '新手琴友',
      '进阶琴友',
      '资深琴友',
      '专业琴友',
      '大师琴友',
    ];
    return level < levels.length ? levels[level] : '未知等级';
  }

  // 检查是否有足够金币
  bool hasEnoughCoins(int requiredCoins) {
    return coins >= requiredCoins;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, nickname: $nickname, level: $level, coins: $coins)';
  }
}
