import 'package:hive/hive.dart';
import 'user_model.dart';

part 'post_model.g.dart';

@HiveType(typeId: 1)
class PostModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String content;

  @HiveField(3)
  List<String> images;

  @HiveField(4)
  String category;

  @HiveField(5)
  String? musicStyle;

  @HiveField(6)
  String? playingLevel;

  @HiveField(7)
  List<String> tags;

  @HiveField(8)
  String authorId;

  @HiveField(9)
  UserModel? author;

  @HiveField(10)
  int likesCount;

  @HiveField(11)
  int commentsCount;

  @HiveField(12)
  int collectionsCount;

  @HiveField(13)
  int sharesCount;

  @HiveField(14)
  bool isLiked;

  @HiveField(15)
  bool isCollected;

  @HiveField(16)
  bool isPinned; // 是否置顶

  @HiveField(17)
  bool isHighlighted; // 是否精华

  @HiveField(18)
  DateTime createdAt;

  @HiveField(19)
  DateTime updatedAt;

  @HiveField(20)
  int viewsCount;

  @HiveField(21)
  bool isDeleted;

  PostModel({
    required this.id,
    required this.title,
    required this.content,
    this.images = const [],
    required this.category,
    this.musicStyle,
    this.playingLevel,
    this.tags = const [],
    required this.authorId,
    this.author,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.collectionsCount = 0,
    this.sharesCount = 0,
    this.isLiked = false,
    this.isCollected = false,
    this.isPinned = false,
    this.isHighlighted = false,
    required this.createdAt,
    required this.updatedAt,
    this.viewsCount = 0,
    this.isDeleted = false,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      images: List<String>.from(json['images'] as List? ?? []),
      category: json['category'] as String,
      musicStyle: json['music_style'] as String?,
      playingLevel: json['playing_level'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      authorId: json['author_id'] as String,
      author: json['author'] != null
          ? UserModel.fromJson(json['author'] as Map<String, dynamic>)
          : null,
      likesCount: json['likes_count'] as int? ?? 0,
      commentsCount: json['comments_count'] as int? ?? 0,
      collectionsCount: json['collections_count'] as int? ?? 0,
      sharesCount: json['shares_count'] as int? ?? 0,
      isLiked: json['is_liked'] as bool? ?? false,
      isCollected: json['is_collected'] as bool? ?? false,
      isPinned: json['is_pinned'] as bool? ?? false,
      isHighlighted: json['is_highlighted'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      viewsCount: json['views_count'] as int? ?? 0,
      isDeleted: json['is_deleted'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'images': images,
      'category': category,
      'music_style': musicStyle,
      'playing_level': playingLevel,
      'tags': tags,
      'author_id': authorId,
      'author': author?.toJson(),
      'likes_count': likesCount,
      'comments_count': commentsCount,
      'collections_count': collectionsCount,
      'shares_count': sharesCount,
      'is_liked': isLiked,
      'is_collected': isCollected,
      'is_pinned': isPinned,
      'is_highlighted': isHighlighted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'views_count': viewsCount,
      'is_deleted': isDeleted,
    };
  }

  PostModel copyWith({
    String? id,
    String? title,
    String? content,
    List<String>? images,
    String? category,
    String? musicStyle,
    String? playingLevel,
    List<String>? tags,
    String? authorId,
    UserModel? author,
    int? likesCount,
    int? commentsCount,
    int? collectionsCount,
    int? sharesCount,
    bool? isLiked,
    bool? isCollected,
    bool? isPinned,
    bool? isHighlighted,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? viewsCount,
    bool? isDeleted,
  }) {
    return PostModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      images: images ?? this.images,
      category: category ?? this.category,
      musicStyle: musicStyle ?? this.musicStyle,
      playingLevel: playingLevel ?? this.playingLevel,
      tags: tags ?? this.tags,
      authorId: authorId ?? this.authorId,
      author: author ?? this.author,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      collectionsCount: collectionsCount ?? this.collectionsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      isLiked: isLiked ?? this.isLiked,
      isCollected: isCollected ?? this.isCollected,
      isPinned: isPinned ?? this.isPinned,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      viewsCount: viewsCount ?? this.viewsCount,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  // 获取格式化的创建时间
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return '${months[createdAt.month - 1]} ${createdAt.day}';
    }
  }

  // 获取内容预览（限制字数）
  String getContentPreview({int maxLength = 100}) {
    if (content.length <= maxLength) {
      return content;
    }
    return '${content.substring(0, maxLength)}...';
  }

  // 获取主图片
  String? get primaryImage {
    return images.isNotEmpty ? images.first : null;
  }

  @override
  String toString() {
    return 'PostModel(id: $id, title: $title, category: $category, likesCount: $likesCount)';
  }
}
