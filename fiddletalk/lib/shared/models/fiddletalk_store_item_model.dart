import '../../core/constants/store_goods_constants.dart';

/// FiddleTalk商店商品实体模型
class FiddleTalkStoreItemModel {
  final String code;
  final double price;
  final int exchangeCoin;
  final String? tags;

  const FiddleTalkStoreItemModel({
    required this.code,
    required this.price,
    required this.exchangeCoin,
    this.tags,
  });

  /// 是否为促销商品
  bool get isPromotion => tags?.contains('Big Deal') == true;

  /// 是否为测试商品（已移除所有测试商品）不需要了
  bool get isTestItem => false;

  /// 获取显示名称 - 使用统一数据源
  String get displayName {
    return StoreGoodsConstants.getDisplayName(code);
  }

  /// 获取描述 - 使用统一数据源
  String get description {
    return StoreGoodsConstants.getDescription(code);
  }

  /// 获取图标 - 使用统一数据源
  String get iconPath {
    return StoreGoodsConstants.getIconPath(code);
  }

  /// 获取所有商品 - 使用统一数据源
  static List<FiddleTalkStoreItemModel> getAllItems() {
    return StoreGoodsConstants.toStoreItemModels();
  }

  @override
  String toString() {
    return 'FiddleTalkStoreItemModel(code: $code, price: $price, exchangeCoin: $exchangeCoin, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FiddleTalkStoreItemModel && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
} 