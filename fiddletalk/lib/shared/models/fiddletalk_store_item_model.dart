/// FiddleTalk商店商品实体模型
class FiddleTalkStoreItemModel {
  final String code;
  final double price;
  final int exchangeCoin;
  final String? tags;

  const FiddleTalkStoreItemModel({
    required this.code,
    required this.price,
    required this.exchangeCoin,
    this.tags,
  });

  /// 是否为促销商品
  bool get isPromotion => tags?.contains('Big Deal') == true;

  /// 是否为测试商品（已移除所有测试商品）不需要了
  bool get isTestItem => false;

  /// 获取显示名称
  String get displayName {
    // 根据金币数量生成名称
    if (exchangeCoin >= 10000) {
      return 'Premium Collection';
    } else if (exchangeCoin >= 5000) {
      return 'Professional Pack';
    } else if (exchangeCoin >= 1000) {
      return 'Advanced Pack';
    } else if (exchangeCoin >= 500) {
      return 'Standard Pack';
    } else {
      return 'Basic Pack';
    }
  }

  /// 获取描述
  String get description {
    if (isPromotion) {
      return 'Special promotional offer! Limited time deal with extra value.';
    }
    return 'Premium coins for FiddleTalk features and content creation.';
  }

  /// 获取图标
  String get iconPath {
    if (isPromotion) {
      return 'assets/icons/coin_premium.png';
    }

    // 根据金币数量分配图标
    if (exchangeCoin >= 10000) {
      return 'assets/icons/coin_premium.png';
    } else if (exchangeCoin >= 1000) {
      return 'assets/icons/coin_standard.png';
    } else {
      return 'assets/icons/coin_basic.png';
    }
  }

  /// 获取所有商品
  static List<FiddleTalkStoreItemModel> getAllItems() {
    return [
      // 普通商品
      const FiddleTalkStoreItemModel(
        code: '311400',
        price: 0.99,
        exchangeCoin: 100,
      ),
      const FiddleTalkStoreItemModel(
        code: '395401',
        price: 3.99,
        exchangeCoin: 399,
      ),
      const FiddleTalkStoreItemModel(
        code: '395402',
        price: 4.99,
        exchangeCoin: 499,
      ),
      const FiddleTalkStoreItemModel(
        code: '395403',
        price: 9.99,
        exchangeCoin: 999,
      ),
      const FiddleTalkStoreItemModel(
        code: '395404',
        price: 12.99,
        exchangeCoin: 1299,
      ),
      const FiddleTalkStoreItemModel(
        code: '395405',
        price: 19.99,
        exchangeCoin: 2500,
      ),
      const FiddleTalkStoreItemModel(
        code: '395406',
        price: 29.99,
        exchangeCoin: 3749,
      ),
      const FiddleTalkStoreItemModel(
        code: '395407',
        price: 49.99,
        exchangeCoin: 7000,
      ),
      const FiddleTalkStoreItemModel(
        code: '395408',
        price: 99.99,
        exchangeCoin: 15000,
      ),

      // 促销商品
      const FiddleTalkStoreItemModel(
        code: '395409',
        price: 1.99,
        exchangeCoin: 500,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395410',
        price: 4.99,
        exchangeCoin: 1200,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395411',
        price: 11.99,
        exchangeCoin: 2500,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395412',
        price: 34.99,
        exchangeCoin: 7000,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395413',
        price: 49.99,
        exchangeCoin: 10000,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395414',
        price: 79.99,
        exchangeCoin: 15000,
        tags: 'Big Deal',
      ),
      const FiddleTalkStoreItemModel(
        code: '395415',
        price: 99.99,
        exchangeCoin: 17888,
        tags: 'Big Deal',
      ),
    ];
  }

  @override
  String toString() {
    return 'FiddleTalkStoreItemModel(code: $code, price: $price, exchangeCoin: $exchangeCoin, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FiddleTalkStoreItemModel && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
} 