/// Report reason enumeration
enum ReportReason {
  spam('Spam', 'Publishing repetitive, meaningless content'),
  harassment('Harassment', 'Malicious harassment, abuse or threats to other users'),
  inappropriate('Inappropriate Content', 'Publishing inappropriate images, text or videos'),
  copyright('Copyright Infringement', 'Unauthorized use of others\' music works or content'),
  misinformation('Misinformation', 'Deliberately spreading false or misleading information'),
  violence('Violent Content', 'Contains violence, gore or dangerous behavior'),
  hate('Hate Speech', 'Discriminatory speech based on race, gender, religion, etc.'),
  other('Other', 'Other inappropriate behavior');

  const ReportReason(this.title, this.description);

  final String title;
  final String description;

  /// Get all report reasons
  static List<ReportReason> get allReasons => ReportReason.values;

  /// Get report reason by title
  static ReportReason? fromTitle(String title) {
    try {
      return ReportReason.values.firstWhere((reason) => reason.title == title);
    } catch (e) {
      return null;
    }
  }
}

/// Report record model
class ReportModel {
  final String id;
  final String reporterId; // 举报人ID
  final String reportedUserId; // 被举报用户ID
  final String? reportedPostId; // 被举报帖子ID（可选）
  final List<ReportReason> reasons; // 举报原因
  final String? additionalInfo; // 额外说明
  final DateTime createdAt;
  final ReportStatus status;

  const ReportModel({
    required this.id,
    required this.reporterId,
    required this.reportedUserId,
    this.reportedPostId,
    required this.reasons,
    this.additionalInfo,
    required this.createdAt,
    this.status = ReportStatus.pending,
  });

  /// 创建副本
  ReportModel copyWith({
    String? id,
    String? reporterId,
    String? reportedUserId,
    String? reportedPostId,
    List<ReportReason>? reasons,
    String? additionalInfo,
    DateTime? createdAt,
    ReportStatus? status,
  }) {
    return ReportModel(
      id: id ?? this.id,
      reporterId: reporterId ?? this.reporterId,
      reportedUserId: reportedUserId ?? this.reportedUserId,
      reportedPostId: reportedPostId ?? this.reportedPostId,
      reasons: reasons ?? this.reasons,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reporterId': reporterId,
      'reportedUserId': reportedUserId,
      'reportedPostId': reportedPostId,
      'reasons': reasons.map((r) => r.title).toList(),
      'additionalInfo': additionalInfo,
      'createdAt': createdAt.toIso8601String(),
      'status': status.name,
    };
  }

  /// 从JSON创建
  factory ReportModel.fromJson(Map<String, dynamic> json) {
    return ReportModel(
      id: json['id'] as String,
      reporterId: json['reporterId'] as String,
      reportedUserId: json['reportedUserId'] as String,
      reportedPostId: json['reportedPostId'] as String?,
      reasons: (json['reasons'] as List<dynamic>)
          .map((r) => ReportReason.fromTitle(r as String))
          .where((r) => r != null)
          .cast<ReportReason>()
          .toList(),
      additionalInfo: json['additionalInfo'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      status: ReportStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ReportStatus.pending,
      ),
    );
  }

  @override
  String toString() {
    return 'ReportModel(id: $id, reportedUserId: $reportedUserId, reasons: ${reasons.map((r) => r.title).join(", ")})';
  }
}

/// Report status enumeration
enum ReportStatus {
  pending('Pending'),
  reviewing('Reviewing'),
  resolved('Resolved'),
  dismissed('Dismissed');

  const ReportStatus(this.displayName);

  final String displayName;
}

/// Block record model
class BlockModel {
  final String id;
  final String blockerId; // 拉黑人ID
  final String blockedUserId; // 被拉黑用户ID
  final DateTime createdAt;

  const BlockModel({
    required this.id,
    required this.blockerId,
    required this.blockedUserId,
    required this.createdAt,
  });

  /// 创建副本
  BlockModel copyWith({
    String? id,
    String? blockerId,
    String? blockedUserId,
    DateTime? createdAt,
  }) {
    return BlockModel(
      id: id ?? this.id,
      blockerId: blockerId ?? this.blockerId,
      blockedUserId: blockedUserId ?? this.blockedUserId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockerId': blockerId,
      'blockedUserId': blockedUserId,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory BlockModel.fromJson(Map<String, dynamic> json) {
    return BlockModel(
      id: json['id'] as String,
      blockerId: json['blockerId'] as String,
      blockedUserId: json['blockedUserId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  @override
  String toString() {
    return 'BlockModel(id: $id, blockedUserId: $blockedUserId)';
  }
}
