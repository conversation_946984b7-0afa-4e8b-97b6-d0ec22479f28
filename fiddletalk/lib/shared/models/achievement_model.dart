import 'package:hive/hive.dart';

part 'achievement_model.g.dart';

/// Achievement types
@HiveType(typeId: 5)
enum AchievementType {
  @HiveField(0)
  posts,   // 发布帖子成就
  @HiveField(1)
  comments // 发布评论成就
}

/// Achievement model
@HiveType(typeId: 4)
class AchievementModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String description;

  @HiveField(3)
  String icon;

  @HiveField(4)
  AchievementType type;

  @HiveField(5)
  int targetCount; // 目标数量

  @HiveField(6)
  int currentCount; // 当前数量

  @HiveField(7)
  bool isUnlocked; // 是否解锁

  @HiveField(8)
  DateTime? unlockedAt; // 解锁时间

  @HiveField(9)
  DateTime createdAt;

  AchievementModel({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.targetCount,
    this.currentCount = 0,
    this.isUnlocked = false,
    this.unlockedAt,
    required this.createdAt,
  });

  /// Progress percentage (0.0 to 1.0)
  double get progress => targetCount > 0 ? (currentCount / targetCount).clamp(0.0, 1.0) : 0.0;

  /// Check if achievement can be unlocked
  bool get canUnlock => !isUnlocked && currentCount >= targetCount;

  /// Update progress
  AchievementModel updateProgress(int newCount) {
    final updated = AchievementModel(
      id: id,
      title: title,
      description: description,
      icon: icon,
      type: type,
      targetCount: targetCount,
      currentCount: newCount,
      isUnlocked: newCount >= targetCount ? true : isUnlocked,
      unlockedAt: (newCount >= targetCount && !isUnlocked) ? DateTime.now() : unlockedAt,
      createdAt: createdAt,
    );
    return updated;
  }

  /// Copy with method
  AchievementModel copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    AchievementType? type,
    int? targetCount,
    int? currentCount,
    bool? isUnlocked,
    DateTime? unlockedAt,
    DateTime? createdAt,
  }) {
    return AchievementModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }
} 