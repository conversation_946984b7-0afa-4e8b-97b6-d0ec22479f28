// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 0;

  @override
  UserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserModel(
      id: fields[0] as String,
      username: fields[1] as String,
      nickname: fields[2] as String,
      avatar: fields[3] as String?,
      bio: fields[4] as String?,
      email: fields[5] as String,
      phone: fields[6] as String?,
      level: fields[7] as int,
      coins: fields[8] as int,
      followersCount: fields[9] as int,
      followingCount: fields[10] as int,
      postsCount: fields[11] as int,
      badges: (fields[12] as List).cast<String>(),
      createdAt: fields[13] as DateTime,
      updatedAt: fields[14] as DateTime,
      isVerified: fields[15] as bool,
      learningYears: fields[16] as String?,
      specialtyStyle: fields[17] as String?,
      personalGoal: fields[18] as String?,
      isOnline: fields[19] as bool,
      lastActiveAt: fields[20] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.username)
      ..writeByte(2)
      ..write(obj.nickname)
      ..writeByte(3)
      ..write(obj.avatar)
      ..writeByte(4)
      ..write(obj.bio)
      ..writeByte(5)
      ..write(obj.email)
      ..writeByte(6)
      ..write(obj.phone)
      ..writeByte(7)
      ..write(obj.level)
      ..writeByte(8)
      ..write(obj.coins)
      ..writeByte(9)
      ..write(obj.followersCount)
      ..writeByte(10)
      ..write(obj.followingCount)
      ..writeByte(11)
      ..write(obj.postsCount)
      ..writeByte(12)
      ..write(obj.badges)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.isVerified)
      ..writeByte(16)
      ..write(obj.learningYears)
      ..writeByte(17)
      ..write(obj.specialtyStyle)
      ..writeByte(18)
      ..write(obj.personalGoal)
      ..writeByte(19)
      ..write(obj.isOnline)
      ..writeByte(20)
      ..write(obj.lastActiveAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
