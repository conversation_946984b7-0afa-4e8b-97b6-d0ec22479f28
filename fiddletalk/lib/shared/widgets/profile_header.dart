import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../core/services/achievement_service.dart';
import '../../shared/models/achievement_model.dart';

/// Profile header component
class ProfileHeader extends StatelessWidget {
  final String? avatar;
  final String nickname;
  final String? bio;
  final int level;
  final int coins;
  final List<String> badges;
  final String? learningYears;
  final String? specialtyStyle;
  final String? personalGoal;
  final VoidCallback? onEditProfile;

  const ProfileHeader({
    super.key,
    this.avatar,
    required this.nickname,
    this.bio,
    required this.level,
    required this.coins,
    this.badges = const [],
    this.learningYears,
    this.specialtyStyle,
    this.personalGoal,
    this.onEditProfile,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.surface,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Avatar and basic info
          Row(
            children: [
              // Avatar
              _buildAvatar(),
              
              const SizedBox(width: 16),
              
              // Basic info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nickname and level
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            nickname,
                            style: AppTextStyles.headline6.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildLevelBadge(),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Coins display
                    _buildCoinsDisplay(),
                    
                    const SizedBox(height: 8),
                    
                    // Edit profile button
                    _buildEditButton(),
                  ],
                ),
              ),
            ],
          ),
          
          // Personal bio
          if (bio != null && bio!.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildBioSection(),
          ],
          
          // Detailed info
          const SizedBox(height: 16),
          _buildDetailInfo(),
          
          // Badges display
          if (badges.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildBadgesSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 3,
        ),
      ),
      child: ClipOval(
        child: avatar != null
            ? _buildAvatarImage()
            : Container(
                color: AppColors.surfaceVariant,
                child: const Icon(
                  Icons.person,
                  size: 40,
                  color: AppColors.textTertiary,
                ),
              ),
      ),
    );
  }

  Widget _buildAvatarImage() {
    if (avatar!.startsWith('assets/')) {
      // Asset image
      return Image.asset(
        avatar!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: AppColors.surfaceVariant,
          child: const Icon(
            Icons.person,
            size: 40,
            color: AppColors.textTertiary,
          ),
        ),
      );
    } else if (avatar!.startsWith('http://') || avatar!.startsWith('https://')) {
      // Network image
      return CachedNetworkImage(
        imageUrl: avatar!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: AppColors.surfaceVariant,
          child: const Icon(
            Icons.person,
            size: 40,
            color: AppColors.textTertiary,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppColors.surfaceVariant,
          child: const Icon(
            Icons.person,
            size: 40,
            color: AppColors.textTertiary,
          ),
        ),
      );
    } else {
      // Local file image
      return Image.file(
        File(avatar!),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: AppColors.surfaceVariant,
          child: const Icon(
            Icons.person,
            size: 40,
            color: AppColors.textTertiary,
          ),
        ),
      );
    }
  }

  Widget _buildLevelBadge() {
    final levelName = level < AppConstants.userLevels.length 
        ? AppConstants.userLevels[level] 
        : '未知等级';
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        levelName,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textOnPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCoinsDisplay() {
    return Row(
      children: [
        Icon(
          Icons.monetization_on,
          size: 16,
          color: AppColors.accent,
        ),
        const SizedBox(width: 4),
        Text(
          '$coins Coins',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.accent,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildEditButton() {
    return GestureDetector(
      onTap: onEditProfile,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          'Edit Profile',
          style: AppTextStyles.caption.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildBioSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        bio!,
        style: AppTextStyles.body2.copyWith(
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildDetailInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          if (learningYears != null)
            _buildInfoRow('Learning Years', learningYears!),

          if (specialtyStyle != null) ...[
            if (learningYears != null) const SizedBox(height: 8),
            _buildInfoRow('Specialty Style', specialtyStyle!),
          ],

          if (personalGoal != null) ...[
            if (learningYears != null || specialtyStyle != null)
              const SizedBox(height: 8),
            _buildInfoRow('Personal Goal', personalGoal!),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.body2,
          ),
        ),
      ],
    );
  }

  Widget _buildBadgesSection() {
    return FutureBuilder<List<AchievementModel>>(
      future: _getUnlockedAchievements(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final unlockedAchievements = snapshot.data!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Badges',
              style: AppTextStyles.subtitle2.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 12),
            
            unlockedAchievements.isEmpty
                ? Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.divider),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.emoji_events_outlined,
                          color: AppColors.textTertiary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'No achievements unlocked yet',
                          style: AppTextStyles.body2.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  )
                : Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: unlockedAchievements.take(3).map((achievement) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: AppColors.accentGradient,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              achievement.icon,
                              style: const TextStyle(fontSize: 14),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              achievement.title,
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.textOnPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
          ],
        );
      },
    );
  }

  Future<List<AchievementModel>> _getUnlockedAchievements() async {
    final achievementService = AchievementService();
    // Wait for service to initialize if needed
    int attempts = 0;
    while (!achievementService.isInitialized && attempts < 50) {
      await Future.delayed(const Duration(milliseconds: 100));
      attempts++;
    }
    return achievementService.unlockedAchievements;
  }
}
