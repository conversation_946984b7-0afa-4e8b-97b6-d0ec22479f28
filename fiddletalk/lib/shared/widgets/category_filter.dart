import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// 分类筛选组件 - 用于首页的内容分类筛选
class CategoryFilter extends StatelessWidget {
  final String title;
  final List<String> categories;
  final String selectedCategory;
  final ValueChanged<String> onCategoryChanged;
  final bool showTitle;

  const CategoryFilter({
    super.key,
    required this.title,
    required this.categories,
    required this.selectedCategory,
    required this.onCategoryChanged,
    this.showTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            title,
            style: AppTextStyles.subtitle2.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = category == selectedCategory;

              return Padding(
                padding: EdgeInsets.only(
                  right: index < categories.length - 1 ? 12 : 0,
                ),
                child: _buildCategoryChip(
                  category: category,
                  isSelected: isSelected,
                  onTap: () => onCategoryChanged(category),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip({
    required String category,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
          border: isSelected
              ? null
              : Border.all(
                  color: AppColors.border.withOpacity(0.5),
                  width: 1,
                ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    offset: const Offset(0, 4),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                ]
              : null,
        ),
        child: Text(
          category,
          style: AppTextStyles.body2.copyWith(
            color: isSelected
                ? Colors.white
                : AppColors.textSecondary,
            fontWeight: isSelected
                ? FontWeight.bold
                : FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

/// 水平滚动的标签筛选器
class HorizontalTagFilter extends StatelessWidget {
  final List<String> tags;
  final List<String> selectedTags;
  final ValueChanged<List<String>> onTagsChanged;
  final bool multiSelect;

  const HorizontalTagFilter({
    super.key,
    required this.tags,
    required this.selectedTags,
    required this.onTagsChanged,
    this.multiSelect = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 36,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: tags.length,
        itemBuilder: (context, index) {
          final tag = tags[index];
          final isSelected = selectedTags.contains(tag);
          
          return Padding(
            padding: EdgeInsets.only(
              right: index < tags.length - 1 ? 8 : 0,
            ),
            child: _buildTagChip(
              tag: tag,
              isSelected: isSelected,
              onTap: () => _onTagTap(tag),
            ),
          );
        },
      ),
    );
  }

  void _onTagTap(String tag) {
    List<String> newSelectedTags = List.from(selectedTags);
    
    if (multiSelect) {
      if (newSelectedTags.contains(tag)) {
        newSelectedTags.remove(tag);
      } else {
        newSelectedTags.add(tag);
      }
    } else {
      newSelectedTags = [tag];
    }
    
    onTagsChanged(newSelectedTags);
  }

  Widget _buildTagChip({
    required String tag,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.accent.withOpacity(0.1)
              : AppColors.surface,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: isSelected 
                ? AppColors.accent 
                : AppColors.border,
            width: 1,
          ),
        ),
        child: Text(
          tag,
          style: AppTextStyles.caption.copyWith(
            color: isSelected 
                ? AppColors.accent 
                : AppColors.textSecondary,
            fontWeight: isSelected 
                ? FontWeight.w600 
                : FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

/// 网格布局的分类选择器
class GridCategorySelector extends StatelessWidget {
  final List<CategoryItem> categories;
  final String? selectedCategory;
  final ValueChanged<String> onCategorySelected;
  final int crossAxisCount;

  const GridCategorySelector({
    super.key,
    required this.categories,
    this.selectedCategory,
    required this.onCategorySelected,
    this.crossAxisCount = 2,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final isSelected = category.name == selectedCategory;
        
        return _buildCategoryItem(
          category: category,
          isSelected: isSelected,
          onTap: () => onCategorySelected(category.name),
        );
      },
    );
  }

  Widget _buildCategoryItem({
    required CategoryItem category,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primary.withOpacity(0.1)
              : AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? AppColors.primary 
                : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              category.icon,
              size: 20,
              color: isSelected 
                  ? AppColors.primary 
                  : AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                category.name,
                style: AppTextStyles.body2.copyWith(
                  color: isSelected 
                      ? AppColors.primary 
                      : AppColors.textPrimary,
                  fontWeight: isSelected 
                      ? FontWeight.w600 
                      : FontWeight.w400,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 分类项目数据模型
class CategoryItem {
  final String name;
  final IconData icon;
  final String? description;

  const CategoryItem({
    required this.name,
    required this.icon,
    this.description,
  });
}
