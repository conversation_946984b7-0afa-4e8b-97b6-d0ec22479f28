import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// 动态卡片组件 - 用于社区页面展示用户动态
class DynamicCard extends StatelessWidget {
  final String avatar;
  final String username;
  final String timeAgo;
  final String content;
  final List<String> images;
  final List<String> tags;
  final int likesCount;
  final int commentsCount;
  final bool isLiked;
  final VoidCallback? onTap;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onUserTap;

  const DynamicCard({
    super.key,
    required this.avatar,
    required this.username,
    required this.timeAgo,
    required this.content,
    this.images = const [],
    this.tags = const [],
    required this.likesCount,
    required this.commentsCount,
    this.isLiked = false,
    this.onTap,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onUserTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息栏
            _buildUserHeader(),
            
            const SizedBox(height: 12),
            
            // 动态内容
            Text(
              content,
              style: AppTextStyles.body1.copyWith(
                height: 1.5,
              ),
            ),
            
            // 图片网格
            if (images.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildImageGrid(),
            ],
            
            // 标签
            if (tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildTags(),
            ],
            
            const SizedBox(height: 16),
            
            // 互动按钮栏
            _buildInteractionBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader() {
    return Row(
      children: [
        // 用户头像
        GestureDetector(
          onTap: onUserTap,
          child: CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.surfaceVariant,
            backgroundImage: _getAvatarImageProvider(avatar),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 用户名和时间
        Expanded(
          child: GestureDetector(
            onTap: onUserTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username,
                  style: AppTextStyles.subtitle2.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  timeAgo,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // 更多按钮
        Builder(
          builder: (context) => IconButton(
            onPressed: () {
              _showMoreOptions(context);
            },
            icon: const Icon(
              Icons.more_horiz,
              color: AppColors.textTertiary,
            ),
            constraints: const BoxConstraints(),
            padding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  Widget _buildImageGrid() {
    if (images.isEmpty) return const SizedBox.shrink();
    
    if (images.length == 1) {
      return _buildSingleImage(images.first);
    } else if (images.length <= 4) {
      return _buildMultipleImages();
    } else {
      return _buildMultipleImages(maxImages: 4);
    }
  }

  Widget _buildSingleImage(String imageUrl) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppColors.surfaceVariant,
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: AppColors.primary,
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: AppColors.surfaceVariant,
            child: const Icon(
              Icons.image_not_supported_outlined,
              color: AppColors.textTertiary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMultipleImages({int maxImages = 9}) {
    final displayImages = images.take(maxImages).toList();
    final hasMore = images.length > maxImages;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: displayImages.length == 2 ? 2 : 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: displayImages.length,
      itemBuilder: (context, index) {
        final isLast = index == displayImages.length - 1;
        final showMoreOverlay = hasMore && isLast;
        
        return ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: displayImages[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.surfaceVariant,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 1,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.surfaceVariant,
                  child: const Icon(
                    Icons.image_not_supported_outlined,
                    color: AppColors.textTertiary,
                    size: 20,
                  ),
                ),
              ),
              
              if (showMoreOverlay)
                Container(
                  color: Colors.black54,
                  child: Center(
                    child: Text(
                      '+${images.length - maxImages}',
                      style: AppTextStyles.subtitle1.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 8,
      runSpacing: 6,
      children: tags.map((tag) {
        return GestureDetector(
          onTap: () {
            // TODO: 跳转到话题页面
          },
          child: Text(
            tag,
            style: AppTextStyles.body2.copyWith(
              color: AppColors.interactive,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInteractionBar() {
    return Row(
      children: [
        // 点赞按钮
        _buildInteractionButton(
          icon: isLiked ? Icons.favorite : Icons.favorite_border,
          count: likesCount,
          color: isLiked ? AppColors.error : AppColors.textTertiary,
          onTap: onLike,
        ),
        
        const SizedBox(width: 24),
        
        // 评论按钮
        _buildInteractionButton(
          icon: Icons.chat_bubble_outline,
          count: commentsCount,
          color: AppColors.textTertiary,
          onTap: onComment,
        ),
        
        const Spacer(),
        
        // 分享按钮
        IconButton(
          onPressed: onShare,
          icon: const Icon(
            Icons.share_outlined,
            size: 20,
            color: AppColors.textTertiary,
          ),
          constraints: const BoxConstraints(),
          padding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 20,
            color: color,
          ),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Text(
              count > 999 ? '999+' : count.toString(),
              style: AppTextStyles.body2.copyWith(
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 拖拽指示器
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.divider,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // 选项列表
              ListTile(
                leading: const Icon(Icons.report_outlined),
                title: const Text('举报'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 举报功能
                },
              ),
              
              ListTile(
                leading: const Icon(Icons.block_outlined),
                title: const Text('拉黑用户'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 拉黑功能
                },
              ),
              
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  ImageProvider _getAvatarImageProvider(String avatar) {
    if (avatar.startsWith('assets/')) {
      return AssetImage(avatar);
    } else if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
      return CachedNetworkImageProvider(avatar);
    } else {
      return FileImage(File(avatar));
    }
  }
}
