import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// Floating create button component - for community page publishing
class FloatingCreateButton extends StatefulWidget {
  final VoidCallback onPressed;
  final String? tooltip;
  final IconData? icon;

  const FloatingCreateButton({
    super.key,
    required this.onPressed,
    this.tooltip = 'Create Content',
    this.icon = Icons.add,
  });

  @override
  State<FloatingCreateButton> createState() => _FloatingCreateButtonState();
}

class _FloatingCreateButtonState extends State<FloatingCreateButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45度旋转 (45/360 = 0.125)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: FloatingActionButton(
              onPressed: () {
                _animationController.forward().then((_) {
                  _animationController.reverse();
                });
                widget.onPressed();
              },
              tooltip: widget.tooltip,
              backgroundColor: AppColors.accent,
              foregroundColor: AppColors.textOnPrimary,
              elevation: 6,
              highlightElevation: 8,
              child: Icon(
                widget.icon,
                size: 28,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Expandable floating action button - supports multiple quick actions
class ExpandableFloatingActionButton extends StatefulWidget {
  final List<FloatingActionItem> items;
  final IconData? mainIcon;
  final String? tooltip;

  const ExpandableFloatingActionButton({
    super.key,
    required this.items,
    this.mainIcon = Icons.add,
    this.tooltip = 'Create Content',
  });

  @override
  State<ExpandableFloatingActionButton> createState() =>
      _ExpandableFloatingActionButtonState();
}

class _ExpandableFloatingActionButtonState
    extends State<ExpandableFloatingActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45度旋转
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // 展开的操作项
        ...widget.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          
          return AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              final delay = index * 0.1;
              final animationValue = (_expandAnimation.value - delay).clamp(0.0, 1.0);
              
              return Transform.translate(
                offset: Offset(0, (1 - animationValue) * 60),
                child: Opacity(
                  opacity: animationValue,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Label
                        if (item.label != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.shadowMedium,
                                  offset: const Offset(0, 2),
                                  blurRadius: 8,
                                ),
                              ],
                            ),
                            child: Text(
                              item.label!,
                              style: AppTextStyles.caption.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        
                        if (item.label != null) const SizedBox(width: 12),
                        
                        // Button
                        FloatingActionButton.small(
                          onPressed: () {
                            _toggle();
                            item.onPressed();
                          },
                          backgroundColor: item.backgroundColor ?? AppColors.primary,
                          foregroundColor: item.foregroundColor ?? AppColors.textOnPrimary,
                          heroTag: 'fab_${index}',
                          child: Icon(item.icon),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        }).toList(),

        // Main button
        AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: FloatingActionButton(
                onPressed: _toggle,
                tooltip: widget.tooltip,
                backgroundColor: AppColors.accent,
                foregroundColor: AppColors.textOnPrimary,
                child: Icon(
                  _isExpanded ? Icons.close : widget.mainIcon,
                  size: 28,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// Floating action item data model
class FloatingActionItem {
  final IconData icon;
  final String? label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const FloatingActionItem({
    required this.icon,
    this.label,
    required this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
  });
}

/// Quick publish button - with pulse animation effect
class PulsingCreateButton extends StatefulWidget {
  final VoidCallback onPressed;
  final String text;
  final IconData? icon;

  const PulsingCreateButton({
    super.key,
    required this.onPressed,
    this.text = 'Publish',
    this.icon = Icons.add,
  });

  @override
  State<PulsingCreateButton> createState() => _PulsingCreateButtonState();
}

class _PulsingCreateButtonState extends State<PulsingCreateButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: FloatingActionButton.extended(
            onPressed: widget.onPressed,
            backgroundColor: AppColors.accent,
            foregroundColor: AppColors.textOnPrimary,
            icon: Icon(widget.icon),
            label: Text(
              widget.text,
              style: AppTextStyles.button.copyWith(
                color: AppColors.textOnPrimary,
              ),
            ),
          ),
        );
      },
    );
  }
}
