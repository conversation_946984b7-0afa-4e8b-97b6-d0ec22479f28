import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/services/speech_service.dart';

/// Voice input widget with real-time speech recognition and animations
class VoiceInputWidget extends StatefulWidget {
  final TextEditingController textController;
  final String hint;

  const VoiceInputWidget({
    super.key,
    required this.textController,
    this.hint = 'Tap microphone to start voice input...',
  });

  @override
  State<VoiceInputWidget> createState() => _VoiceInputWidgetState();
}

class _VoiceInputWidgetState extends State<VoiceInputWidget>
    with TickerProviderStateMixin {
  final SpeechService _speechService = SpeechService();

  bool _isListening = false;
  String _currentText = '';

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _currentText = widget.textController.text;
  }

  void _initializeAnimations() {
    // Pulse animation for recording button
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startListening() async {
    // Haptic feedback
    HapticFeedback.lightImpact();
    
    final success = await _speechService.startRealTimeListening(
      context: context,
      onResult: (text) {
        setState(() {
          _currentText = text;
          widget.textController.text = text;
        });
      },
      onListeningStateChanged: (isListening) {
        setState(() {
          _isListening = isListening;
        });
        
        if (isListening) {
          _pulseController.repeat(reverse: true);
        } else {
          _pulseController.stop();
        }
      },
      onSoundLevelChange: (level) {
        // Sound level callback - not used for UI anymore
      },
    );

    if (!success) {
      setState(() {
        _isListening = false;
      });
    }
  }

  void _stopListening() async {
    // Haptic feedback
    HapticFeedback.lightImpact();
    
    await _speechService.stopListening(
      onListeningStateChanged: (isListening) {
        setState(() {
          _isListening = isListening;
        });
      },
    );
  }

  void _dismissKeyboard() {
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _dismissKeyboard,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildTextInput(),
            const SizedBox(height: 16),
            _buildControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.mic,
          color: AppColors.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          'Voice Input (English Only)',
          style: AppTextStyles.subtitle2.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const Spacer(),
        if (_isListening)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Recording',
              style: AppTextStyles.caption.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTextInput() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
        border: _isListening
            ? Border.all(color: Colors.red, width: 2)
            : null,
      ),
      child: TextFormField(
        controller: widget.textController,
        maxLines: 4,
        style: AppTextStyles.body1,
        textInputAction: TextInputAction.done,
        onFieldSubmitted: (_) => _dismissKeyboard(),
        decoration: InputDecoration(
          hintText: _isListening
              ? 'Speak in English now...'
              : widget.hint,
          hintStyle: AppTextStyles.body1.copyWith(
            color: AppColors.textTertiary,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.all(12),
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Center(
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isListening ? _pulseAnimation.value : 1.0,
            child: GestureDetector(
              onTap: _isListening ? _stopListening : _startListening,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: _isListening ? Colors.red : AppColors.primary,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: (_isListening ? Colors.red : AppColors.primary)
                          .withOpacity(0.3),
                      offset: const Offset(0, 4),
                      blurRadius: 12,
                    ),
                  ],
                ),
                child: Icon(
                  _isListening ? Icons.stop : Icons.mic,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            ),
          );
        },
      ),
    );
  }


}
