import 'dart:io';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// 帖子卡片组件 - 用于首页展示图文内容
class PostCard extends StatelessWidget {
  final String title;
  final String content;
  final String author;
  final String? authorAvatar;
  final String authorId; // 新增：作者ID
  final String category;
  final String? primaryImage;
  final List<String> images;
  final List<String> tags;
  final int likesCount;
  final int commentsCount;
  final int collectionsCount;
  final bool isLiked;
  final bool isCollected;
  final bool isHighlighted;
  final bool isFollowed; // 新增：是否已关注作者
  final String timeAgo;
  final VoidCallback? onTap;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onCollect;
  final VoidCallback? onShare;
  final VoidCallback? onAuthorTap;
  final VoidCallback? onFollow; // 新增：关注回调
  final VoidCallback? onMoreActions; // 新增：更多操作回调

  const PostCard({
    super.key,
    required this.title,
    required this.content,
    required this.author,
    this.authorAvatar,
    required this.authorId,
    required this.category,
    this.primaryImage,
    this.images = const [],
    this.tags = const [],
    required this.likesCount,
    required this.commentsCount,
    this.collectionsCount = 0,
    this.isLiked = false,
    this.isCollected = false,
    this.isHighlighted = false,
    this.isFollowed = false,
    this.timeAgo = '刚刚',
    this.onTap,
    this.onLike,
    this.onComment,
    this.onCollect,
    this.onShare,
    this.onAuthorTap,
    this.onFollow,
    this.onMoreActions,
  });

  @override
  Widget build(BuildContext context) {
    final imageUrl = primaryImage ?? (images.isNotEmpty ? images.first : null);
    final hasImage = imageUrl != null;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        height: hasImage ? 280 : 200, // 固定高度，有图片时更高
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 背景图片或纯色背景
              if (hasImage)
                _buildBackgroundImage(imageUrl!)
              else
                _buildColorBackground(),

              // 渐变遮罩
              _buildGradientOverlay(),

              // 内容区域
              _buildContentOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  // 背景图片
  Widget _buildBackgroundImage(String imageUrl) {
    // 检查是否是本地资源
    if (imageUrl.startsWith('assets/')) {
      return Positioned.fill(
        child: Image.asset(
          imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildColorBackground(),
        ),
      );
    }
    // 检查是否是本地文件路径（用户上传的图片）
    else if (imageUrl.startsWith('/')) {
      return FutureBuilder<bool>(
        future: _checkImageExists(imageUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Positioned.fill(
              child: Container(
                color: AppColors.surfaceVariant,
                child: const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppColors.primary,
                  ),
                ),
              ),
            );
          }

          if (snapshot.data == true) {
            return Positioned.fill(
              child: Image.file(
                File(imageUrl),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('Error loading image: $imageUrl, Error: $error');
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.accent.withOpacity(0.1),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          } else {
            debugPrint('Image file does not exist: $imageUrl');
            return _buildColorBackground();
          }
        },
      );
    }
    // 网络图片
    else {
      return Positioned.fill(
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppColors.surfaceVariant,
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: AppColors.primary,
              ),
            ),
          ),
          errorWidget: (context, url, error) => _buildColorBackground(),
        ),
      );
    }
  }

  // 检查图片文件是否存在
  Future<bool> _checkImageExists(String imagePath) async {
    try {
      final file = File(imagePath);
      return await file.exists();
    } catch (e) {
      debugPrint('Error checking image existence: $e');
      return false;
    }
  }

  // 纯色背景
  Widget _buildColorBackground() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary.withOpacity(0.1),
              AppColors.accent.withOpacity(0.1),
            ],
          ),
        ),
      ),
    );
  }

  // 渐变遮罩
  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.3),
              Colors.black.withOpacity(0.7),
            ],
            stops: const [0.0, 0.6, 1.0],
          ),
        ),
      ),
    );
  }

  // 内容叠加层
  Widget _buildContentOverlay() {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：分类标签、精华标记和更多操作按钮
            Row(
              children: [
                Expanded(child: _buildCategoryAndBadges()),
                _buildMoreActionsButton(),
              ],
            ),

            const Spacer(),

            // 底部：标题、内容、标签、作者信息
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Container(
                  width: double.infinity,
                  child: Text(
                    title,
                    style: AppTextStyles.subtitle1.copyWith(
                      fontWeight: FontWeight.bold,
                      height: 1.3,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black.withOpacity(0.5),
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                ),

                const SizedBox(height: 6),

                // 内容预览
                Container(
                  width: double.infinity,
                  child: Text(
                    content,
                    style: AppTextStyles.body2.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      height: 1.4,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                ),

                const SizedBox(height: 8),

                // 作者信息和互动按钮
                _buildFooter(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryAndBadges() {
    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: [
        // 分类标签
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            category,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ),

        // 精华标记
        if (isHighlighted)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.accent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Featured',
              style: AppTextStyles.caption.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ),
      ],
    );
  }



  Widget _buildFooter() {
    return Row(
      children: [
        // 作者头像
        GestureDetector(
          onTap: onAuthorTap,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: CircleAvatar(
              radius: 12,
              backgroundColor: Colors.white.withOpacity(0.2),
              backgroundImage: authorAvatar != null && authorAvatar!.isNotEmpty
                  ? AssetImage(authorAvatar!)
                  : null,
              child: authorAvatar == null || authorAvatar!.isEmpty
                  ? const Icon(
                      Icons.person,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),
          ),
        ),

        const SizedBox(width: 8),

        // 作者名称和时间
        Expanded(
          child: GestureDetector(
            onTap: onAuthorTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  author,
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                        color: Colors.black.withOpacity(0.3),
                      ),
                    ],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (timeAgo.isNotEmpty)
                  Text(
                    timeAgo,
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 10,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),

        // 关注按钮 (只在有关注回调时显示)
        if (onFollow != null) ...[
          _buildFollowButton(),
          const SizedBox(width: 8),
        ],

        // 互动按钮
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildInteractionButton(
              icon: isLiked ? Icons.favorite : Icons.favorite_border,
              count: likesCount,
              color: isLiked ? Colors.red[300]! : Colors.white.withOpacity(0.8),
              onTap: onLike,
            ),

            const SizedBox(width: 8),

            _buildInteractionButton(
              icon: Icons.chat_bubble_outline,
              count: commentsCount,
              color: Colors.white.withOpacity(0.8),
              onTap: onComment,
            ),

            const SizedBox(width: 8),

            _buildInteractionButton(
              icon: isCollected ? Icons.bookmark : Icons.bookmark_border,
              count: collectionsCount,
              color: isCollected ? AppColors.accent : Colors.white.withOpacity(0.8),
              onTap: onCollect,
            ),
          ],
        ),
      ],
    );
  }



  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
            shadows: [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 2,
                color: Colors.black.withOpacity(0.3),
              ),
            ],
          ),
          if (count > 0) ...[
            const SizedBox(width: 2),
            Text(
              count > 999 ? '999+' : count.toString(),
              style: AppTextStyles.caption.copyWith(
                color: color,
                fontSize: 11,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black.withOpacity(0.3),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建更多操作按钮
  Widget _buildMoreActionsButton() {
    // 如果没有更多操作回调，不显示按钮
    if (onMoreActions == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onMoreActions,
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Icon(
          Icons.more_horiz,
          size: 16,
          color: Colors.white.withOpacity(0.9),
          shadows: [
            Shadow(
              offset: const Offset(0, 1),
              blurRadius: 2,
              color: Colors.black.withOpacity(0.3),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建关注按钮
  Widget _buildFollowButton() {
    return GestureDetector(
      onTap: onFollow,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isFollowed
              ? Colors.white.withOpacity(0.15)
              : AppColors.primary,
          borderRadius: BorderRadius.circular(16),
          border: isFollowed
              ? Border.all(color: Colors.white.withOpacity(0.4), width: 1.5)
              : null,
          boxShadow: isFollowed ? null : [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              offset: const Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isFollowed) ...[
              Icon(
                Icons.check,
                size: 12,
                color: Colors.white,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              isFollowed ? 'Following' : 'Follow',
              style: AppTextStyles.caption.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 10,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black.withOpacity(0.3),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 移除不再使用的方法
  Widget _buildTags() {
    return const SizedBox.shrink(); // 在新设计中不显示标签
  }
}
