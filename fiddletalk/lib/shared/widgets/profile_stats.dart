import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// Profile statistics component (Following和Posts已移除)
class ProfileStats extends StatelessWidget {
  // 保留参数但不再使用，避免修改调用方
  final int followingCount;
  final int postsCount;
  final VoidCallback? onFollowingPressed;
  final VoidCallback? onPostsPressed;

  const ProfileStats({
    super.key,
    required this.followingCount,
    required this.postsCount,
    this.onFollowingPressed,
    this.onPostsPressed,
  });

  @override
  Widget build(BuildContext context) {
    // 返回一个空的SizedBox，不再显示Following和Posts统计
    return const SizedBox.shrink();
  }

  Widget _buildStatItem({
    required int count,
    required String label,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        children: [
          Text(
            _formatCount(count),
            style: AppTextStyles.headline6.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }
}

/// Extended statistics component - supports more stat items
class ExtendedProfileStats extends StatelessWidget {
  final List<StatItem> stats;
  final int crossAxisCount;

  const ExtendedProfileStats({
    super.key,
    required this.stats,
    this.crossAxisCount = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: 1.5,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: stats.length,
        itemBuilder: (context, index) {
          final stat = stats[index];
          return _buildStatCard(stat);
        },
      ),
    );
  }

  Widget _buildStatCard(StatItem stat) {
    return GestureDetector(
      onTap: stat.onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: stat.backgroundColor ?? AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.border,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (stat.icon != null) ...[
              Icon(
                stat.icon,
                size: 24,
                color: stat.iconColor ?? AppColors.primary,
              ),
              const SizedBox(height: 4),
            ],
            
            Text(
              _formatCount(stat.count),
              style: AppTextStyles.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
                color: stat.countColor ?? AppColors.primary,
              ),
            ),
            
            const SizedBox(height: 2),
            
            Text(
              stat.label,
              style: AppTextStyles.caption.copyWith(
                color: stat.labelColor ?? AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }
}

/// Stat item data model
class StatItem {
  final int count;
  final String label;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? countColor;
  final Color? labelColor;
  final VoidCallback? onTap;

  const StatItem({
    required this.count,
    required this.label,
    this.icon,
    this.backgroundColor,
    this.iconColor,
    this.countColor,
    this.labelColor,
    this.onTap,
  });
}

/// Achievement statistics component
class AchievementStats extends StatelessWidget {
  final int totalPosts;
  final int totalLikes;
  final int totalComments;
  final int totalShares;
  final int daysActive;

  const AchievementStats({
    super.key,
    required this.totalPosts,
    required this.totalLikes,
    required this.totalComments,
    required this.totalShares,
    required this.daysActive,
  });

  @override
  Widget build(BuildContext context) {
    final achievements = [
      AchievementItem(
        icon: Icons.article_outlined,
        count: totalPosts,
        label: '发布内容',
        color: AppColors.primary,
      ),
      AchievementItem(
        icon: Icons.favorite_outline,
        count: totalLikes,
        label: '获得点赞',
        color: AppColors.error,
      ),
      AchievementItem(
        icon: Icons.chat_bubble_outline,
        count: totalComments,
        label: '收到评论',
        color: AppColors.interactive,
      ),
      AchievementItem(
        icon: Icons.share_outlined,
        count: totalShares,
        label: '被分享',
        color: AppColors.accent,
      ),
      AchievementItem(
        icon: Icons.calendar_today_outlined,
        count: daysActive,
        label: '活跃天数',
        color: AppColors.success,
      ),
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Achievements',
            style: AppTextStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: achievements.map((achievement) {
              return _buildAchievementChip(achievement);
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementChip(AchievementItem achievement) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: achievement.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: achievement.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            achievement.icon,
            size: 16,
            color: achievement.color,
          ),
          const SizedBox(width: 6),
          Text(
            '${_formatCount(achievement.count)} ${achievement.label}',
            style: AppTextStyles.caption.copyWith(
              color: achievement.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }
}

/// 成就项数据模型
class AchievementItem {
  final IconData icon;
  final int count;
  final String label;
  final Color color;

  const AchievementItem({
    required this.icon,
    required this.count,
    required this.label,
    required this.color,
  });
}
