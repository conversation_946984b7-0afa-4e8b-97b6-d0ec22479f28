import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// Custom search bar component
class CustomSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onSearch;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool enabled;
  final bool readOnly;
  final TextEditingController? controller;
  final Widget? leading;
  final Widget? trailing;
  final bool showClearButton;

  const CustomSearchBar({
    super.key,
    this.hintText = 'Search...',
    this.onSearch,
    this.onChanged,
    this.onTap,
    this.enabled = true,
    this.readOnly = false,
    this.controller,
    this.leading,
    this.trailing,
    this.showClearButton = true,
  });

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  late TextEditingController _controller;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.readOnly ? widget.onTap : null,
      child: Container(
        height: 44,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(22),
          border: Border.all(
            color: _hasFocus ? AppColors.primary : AppColors.border,
            width: _hasFocus ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // 左侧图标
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 8),
              child: widget.leading ?? 
                Icon(
                  Icons.search,
                  size: 20,
                  color: _hasFocus 
                      ? AppColors.primary 
                      : AppColors.textTertiary,
                ),
            ),
            
            // 输入框
            Expanded(
              child: widget.readOnly
                  ? _buildReadOnlyField()
                  : _buildTextField(),
            ),
            
            // 清除按钮
            if (widget.showClearButton && _controller.text.isNotEmpty)
              _buildClearButton(),
            
            // 右侧图标
            if (widget.trailing != null)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: widget.trailing!,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      enabled: widget.enabled,
      style: AppTextStyles.body2,
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: AppTextStyles.body2.copyWith(
          color: AppColors.textTertiary,
        ),
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      onChanged: (value) {
        setState(() {});
        widget.onChanged?.call(value);
      },
      onSubmitted: widget.onSearch,
      onTap: () {
        setState(() {
          _hasFocus = true;
        });
      },
      onTapOutside: (_) {
        setState(() {
          _hasFocus = false;
        });
      },
    );
  }

  Widget _buildReadOnlyField() {
    return Container(
      alignment: Alignment.centerLeft,
      child: Text(
        _controller.text.isEmpty ? widget.hintText : _controller.text,
        style: AppTextStyles.body2.copyWith(
          color: _controller.text.isEmpty 
              ? AppColors.textTertiary 
              : AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildClearButton() {
    return GestureDetector(
      onTap: () {
        _controller.clear();
        setState(() {});
        widget.onChanged?.call('');
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(
          Icons.clear,
          size: 16,
          color: AppColors.textTertiary,
        ),
      ),
    );
  }
}

/// Search suggestions list component
class SearchSuggestionsList extends StatelessWidget {
  final List<String> suggestions;
  final ValueChanged<String> onSuggestionTap;
  final String? query;

  const SearchSuggestionsList({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.query,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: suggestions.length,
        separatorBuilder: (context, index) => const Divider(
          height: 1,
          indent: 16,
          endIndent: 16,
        ),
        itemBuilder: (context, index) {
          final suggestion = suggestions[index];
          return _buildSuggestionItem(suggestion);
        },
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion) {
    return ListTile(
      dense: true,
      leading: const Icon(
        Icons.search,
        size: 20,
        color: AppColors.textTertiary,
      ),
      title: _buildHighlightedText(suggestion),
      onTap: () => onSuggestionTap(suggestion),
    );
  }

  Widget _buildHighlightedText(String text) {
    if (query == null || query!.isEmpty) {
      return Text(
        text,
        style: AppTextStyles.body2,
      );
    }

    final lowerText = text.toLowerCase();
    final lowerQuery = query!.toLowerCase();
    final index = lowerText.indexOf(lowerQuery);

    if (index == -1) {
      return Text(
        text,
        style: AppTextStyles.body2,
      );
    }

    return RichText(
      text: TextSpan(
        style: AppTextStyles.body2,
        children: [
          if (index > 0)
            TextSpan(text: text.substring(0, index)),
          TextSpan(
            text: text.substring(index, index + query!.length),
            style: AppTextStyles.body2.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (index + query!.length < text.length)
            TextSpan(text: text.substring(index + query!.length)),
        ],
      ),
    );
  }
}

/// Search history component
class SearchHistory extends StatelessWidget {
  final List<String> history;
  final ValueChanged<String> onHistoryTap;
  final VoidCallback? onClearHistory;

  const SearchHistory({
    super.key,
    required this.history,
    required this.onHistoryTap,
    this.onClearHistory,
  });

  @override
  Widget build(BuildContext context) {
    if (history.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                'Search History',
                style: AppTextStyles.subtitle2.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (onClearHistory != null)
                GestureDetector(
                  onTap: onClearHistory,
                  child: Text(
                    'Clear',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // History tags
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: history.map((item) {
              return _buildHistoryChip(item);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryChip(String text) {
    return GestureDetector(
      onTap: () => onHistoryTap(text),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppColors.border,
            width: 1,
          ),
        ),
        child: Text(
          text,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }
}
