import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

/// 话题卡片组件 - 用于社区页面展示热门话题
class TopicCard extends StatelessWidget {
  final String title;
  final String description;
  final int participantsCount;
  final int postsCount;
  final bool isHot;
  final bool isFollowing;
  final VoidCallback? onTap;
  final VoidCallback? onFollow;

  const TopicCard({
    super.key,
    required this.title,
    required this.description,
    required this.participantsCount,
    required this.postsCount,
    this.isHot = false,
    this.isFollowing = false,
    this.onTap,
    this.onFollow,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: isHot 
              ? Border.all(color: AppColors.vibrant.withOpacity(0.3), width: 1)
              : null,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 话题标题和热门标记
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.subtitle1.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                
                if (isHot)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.vibrant.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.local_fire_department,
                          size: 14,
                          color: AppColors.vibrant,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '热门',
                          style: AppTextStyles.caption.copyWith(
                            color: AppColors.vibrant,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 话题描述
            Text(
              description,
              style: AppTextStyles.body2.copyWith(
                color: AppColors.textSecondary,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 16),
            
            // 统计信息和关注按钮
            Row(
              children: [
                // 参与人数
                _buildStatItem(
                  icon: Icons.people_outline,
                  count: participantsCount,
                  label: '参与',
                ),
                
                const SizedBox(width: 16),
                
                // 帖子数量
                _buildStatItem(
                  icon: Icons.article_outlined,
                  count: postsCount,
                  label: '帖子',
                ),
                
                const Spacer(),
                
                // 关注按钮
                _buildFollowButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textTertiary,
        ),
        const SizedBox(width: 4),
        Text(
          '${_formatCount(count)} $label',
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textTertiary,
          ),
        ),
      ],
    );
  }

  Widget _buildFollowButton() {
    return GestureDetector(
      onTap: onFollow,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isFollowing
              ? AppColors.surfaceVariant
              : AppColors.primary,
          borderRadius: BorderRadius.circular(20),
          border: isFollowing
              ? Border.all(color: AppColors.primary.withOpacity(0.3), width: 1.5)
              : null,
          boxShadow: isFollowing ? null : [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.2),
              offset: const Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isFollowing) ...[
              Icon(
                Icons.check,
                size: 14,
                color: AppColors.primary,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              isFollowing ? 'Following' : 'Follow',
              style: AppTextStyles.caption.copyWith(
                color: isFollowing
                    ? AppColors.primary
                    : AppColors.textOnPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }
}
